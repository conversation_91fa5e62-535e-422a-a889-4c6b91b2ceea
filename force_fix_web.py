#!/usr/bin/env python3
"""
强制修复Web界面显示问题
"""
import json
import re
import os

def force_fix_mapping_file():
    """强制修复映射文件中的具体问题"""
    print("=== 强制修复映射文件 ===")
    
    mapping_file = "section_image_mapping.json"
    
    # 加载映射文件
    with open(mapping_file, 'r', encoding='utf-8') as f:
        section_map = json.load(f)
    
    # 找到第19章并强制修复
    fixed = False
    for section_id, section_info in section_map.items():
        if "十九、已报告状态下打印报告配置" in section_info.get('section_title', ''):
            print(f"找到目标章节: {section_id}")
            
            for element in section_info.get('section_elements', []):
                if element.get('type') == 'paragraph':
                    content = element.get('content', '')
                    if "打印状态存在" in content and ("r ,0654" in content or "r\n,0654" in content or "r:WAIT,0654" not in content):
                        print(f"发现问题内容，正在修复...")
                        print(f"原始: {repr(content)}")
                        
                        # 强制修复
                        fixed_content = content
                        # 修复各种可能的问题格式
                        fixed_content = re.sub(r'r\s*,\s*0654', 'r:WAIT,0654', fixed_content)
                        fixed_content = re.sub(r'r\s*,\s*0655', 'r:WAIT,0655', fixed_content)
                        fixed_content = re.sub(r'r\s+,\s*0654', 'r:WAIT,0654', fixed_content)
                        fixed_content = re.sub(r'r\s+,\s*0655', 'r:WAIT,0655', fixed_content)
                        fixed_content = re.sub(r'r\n,\s*0654', 'r:WAIT,0654', fixed_content)
                        fixed_content = re.sub(r'r\n,\s*0655', 'r:WAIT,0655', fixed_content)
                        
                        # 清理多余的空白字符
                        fixed_content = re.sub(r'\s+', ' ', fixed_content).strip()
                        
                        element['content'] = fixed_content
                        print(f"修复后: {repr(fixed_content)}")
                        fixed = True
                        break
            break
    
    if fixed:
        # 保存修复后的文件
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(section_map, f, ensure_ascii=False, indent=2)
        print("✅ 映射文件已强制修复并保存")
    else:
        print("❌ 未找到需要修复的内容")
    
    return fixed

def create_emergency_streamlit_fix():
    """创建紧急Streamlit修复版本"""
    print("\n=== 创建紧急Streamlit修复版本 ===")
    
    # 读取当前的streamlit文件
    with open('streamlit_lite.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 确保包含强制修复函数
    fix_function = '''
def emergency_content_fix(content: str) -> str:
    """紧急内容修复函数"""
    if not content:
        return ""
    
    # 强制修复已知的问题模式
    fixed = content
    
    # 修复 r ,0654 -> r:WAIT,0654
    fixed = re.sub(r'r\\s*,\\s*0654', 'r:WAIT,0654', fixed)
    fixed = re.sub(r'r\\s*,\\s*0655', 'r:WAIT,0655', fixed)
    
    # 修复换行符问题
    fixed = re.sub(r'r\\n,\\s*0654', 'r:WAIT,0654', fixed)
    fixed = re.sub(r'r\\n,\\s*0655', 'r:WAIT,0655', fixed)
    
    # 修复制表符问题
    fixed = re.sub(r'r\\t,\\s*0654', 'r:WAIT,0654', fixed)
    fixed = re.sub(r'r\\t,\\s*0655', 'r:WAIT,0655', fixed)
    
    # 清理多余空白
    fixed = re.sub(r'\\s+', ' ', fixed).strip()
    
    return fixed
'''
    
    # 如果还没有这个函数，添加它
    if 'emergency_content_fix' not in content:
        # 在import部分后添加
        import_end = content.find('import re') + len('import re')
        content = content[:import_end] + fix_function + content[import_end:]
    
    # 修改段落显示部分，确保使用紧急修复
    old_pattern = r'cleaned_content = clean_content_for_web_display\(content\)'
    new_pattern = 'cleaned_content = emergency_content_fix(clean_content_for_web_display(content))'
    
    if old_pattern in content:
        content = re.sub(old_pattern, new_pattern, content)
    else:
        # 如果没找到，直接替换内容显示部分
        old_display = r'st\.markdown\(f"\*\*段落内容：\*\* \{cleaned_content\}"\)'
        new_display = 'st.markdown(f"**段落内容：** {emergency_content_fix(cleaned_content)}")'
        content = re.sub(old_display, new_display, content)
    
    # 保存修复版本
    with open('streamlit_lite_fixed.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已创建紧急修复版本: streamlit_lite_fixed.py")

def create_test_page():
    """创建测试页面来验证修复"""
    print("\n=== 创建测试页面 ===")
    
    test_html = '''<!DOCTYPE html>
<html>
<head>
    <title>内容显示测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .problem { background-color: #ffebee; }
        .fixed { background-color: #e8f5e8; }
        .code { font-family: monospace; background-color: #f5f5f5; padding: 5px; }
    </style>
</head>
<body>
    <h1>内容显示测试页面</h1>
    
    <div class="test-case problem">
        <h2>❌ 问题内容（当前Web界面显示）</h2>
        <div class="code">
            打印状态存在r<br>
            ,0654中，打印时间存在r<br>
            ,0655中
        </div>
        <p>问题：缺少":WAIT"字符，有不正确的换行</p>
    </div>
    
    <div class="test-case fixed">
        <h2>✅ 正确内容（应该显示的）</h2>
        <div class="code">
            打印状态存在r:WAIT,0654中，打印时间存在r:WAIT,0655中
        </div>
        <p>正确：包含完整的":WAIT"字符，没有不正确的换行</p>
    </div>
    
    <h2>修复步骤</h2>
    <ol>
        <li>运行 <code>python force_fix_web.py</code></li>
        <li>停止当前的Streamlit应用 (Ctrl+C)</li>
        <li>启动修复版本: <code>streamlit run streamlit_lite_fixed.py</code></li>
        <li>在浏览器中强制刷新页面 (Ctrl+F5)</li>
        <li>搜索"打印状态"测试修复效果</li>
    </ol>
</body>
</html>'''
    
    with open('test_display.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("✅ 已创建测试页面: test_display.html")

def main():
    """主函数"""
    print("🚨 紧急修复Web界面显示问题")
    print("="*50)
    
    # 1. 强制修复映射文件
    mapping_fixed = force_fix_mapping_file()
    
    # 2. 创建紧急修复版本
    create_emergency_streamlit_fix()
    
    # 3. 创建测试页面
    create_test_page()
    
    print("\n" + "="*50)
    print("🎯 紧急修复完成！")
    print("\n请按以下步骤操作:")
    print("1. 停止当前的Streamlit应用 (按 Ctrl+C)")
    print("2. 启动修复版本:")
    print("   streamlit run streamlit_lite_fixed.py --server.port 8501")
    print("3. 在浏览器中强制刷新页面 (Ctrl+F5)")
    print("4. 搜索'打印状态'测试修复效果")
    print("5. 如果仍有问题，请打开 test_display.html 对比显示效果")
    print("="*50)

if __name__ == "__main__":
    main()
