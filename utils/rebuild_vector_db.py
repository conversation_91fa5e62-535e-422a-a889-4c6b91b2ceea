"""
重建向量数据库
"""
import os
import shutil
from rag_system import RAGSystem
import config

def rebuild_vector_database():
    """重建向量数据库"""
    print("🔄 重建向量数据库")
    print("=" * 50)
    
    # 删除旧的向量数据库
    if os.path.exists(config.VECTOR_DB_PATH):
        print(f"删除旧的向量数据库: {config.VECTOR_DB_PATH}")
        shutil.rmtree(config.VECTOR_DB_PATH)
    
    # 初始化RAG系统并强制重建
    rag = RAGSystem()
    
    print("强制重建向量数据库...")
    success = rag.initialize_system(force_rebuild=True)
    
    if success:
        print("✅ 向量数据库重建成功！")
        
        # 测试查询
        print("\n🔍 测试查询功能:")
        test_queries = ["配置", "TERMINAL_LEVEL", "登记工作站"]
        
        for query in test_queries:
            print(f"\n查询: '{query}'")
            result = rag.query(query)
            
            print(f"  回答长度: {len(result['answer'])}")
            print(f"  来源数量: {len(result['sources'])}")
            print(f"  图片数量: {len(result.get('images', []))}")
            
            if result.get('images'):
                for img in result['images'][:2]:
                    print(f"    - {img.get('filename', '未知')} (相关性: {img.get('relevance_score', 0):.2f})")
    else:
        print("❌ 向量数据库重建失败")

if __name__ == "__main__":
    rebuild_vector_database()
