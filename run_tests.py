"""
测试运行器 - 运行所有测试
"""
import sys
import os
import importlib.util

def run_test_file(test_file_path):
    """运行单个测试文件"""
    try:
        print(f"\n{'='*60}")
        print(f"运行测试: {os.path.basename(test_file_path)}")
        print(f"{'='*60}")
        
        # 动态导入并运行测试文件
        spec = importlib.util.spec_from_file_location("test_module", test_file_path)
        test_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        
        print(f"✅ 测试完成: {os.path.basename(test_file_path)}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {os.path.basename(test_file_path)} - {e}")
        return False

def main():
    """主函数"""
    print("🚀 RIS RAG系统测试套件")
    print("=" * 60)
    
    # 获取所有测试文件
    tests_dir = "tests"
    test_files = []
    
    if os.path.exists(tests_dir):
        for file in os.listdir(tests_dir):
            if file.startswith("test_") and file.endswith(".py"):
                test_files.append(os.path.join(tests_dir, file))
    
    if not test_files:
        print("❌ 没有找到测试文件")
        return
    
    print(f"📋 找到 {len(test_files)} 个测试文件:")
    for test_file in test_files:
        print(f"   • {os.path.basename(test_file)}")
    
    # 运行测试
    passed = 0
    failed = 0
    
    for test_file in test_files:
        if run_test_file(test_file):
            passed += 1
        else:
            failed += 1
    
    # 显示结果
    print(f"\n{'='*60}")
    print(f"📊 测试结果汇总")
    print(f"{'='*60}")
    print(f"总测试数: {len(test_files)}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"成功率: {passed/len(test_files)*100:.1f}%")
    
    if failed == 0:
        print(f"\n🎉 所有测试通过！")
    else:
        print(f"\n⚠️ {failed} 个测试失败，请检查相关代码")

if __name__ == "__main__":
    main()
