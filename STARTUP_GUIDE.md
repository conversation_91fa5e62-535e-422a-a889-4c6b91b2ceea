# RIS RAG系统启动指南

## 🚀 快速启动

### 方法1: 使用PowerShell脚本（推荐）
```powershell
# 在项目根目录执行
powershell -ExecutionPolicy Bypass -File quick_start.ps1
```

### 方法2: 使用批处理文件
```cmd
# 双击运行或在命令行执行
quick_start.bat
```

### 方法3: 使用Python启动器
```bash
python start_system.py
```

## 🔧 环境配置

系统会自动设置以下环境变量：
- `HF_ENDPOINT = "https://hf-mirror.com"` - Hugging Face镜像
- `TOKENIZERS_PARALLELISM = "false"` - 避免tokenizer警告

## 🌐 访问地址

启动成功后，可通过以下地址访问：
- **本地访问**: http://localhost:8512
- **网络访问**: http://***********:8512 (根据实际IP)

## 📋 启动检查清单

启动前系统会自动检查：
- ✅ RIS问题及解决方法.docx 文档文件
- ✅ .venv 虚拟环境
- ✅ src 源代码目录
- ✅ 环境变量设置

## 🎯 功能测试

启动后建议测试以下查询：
1. **"集成UV"** - 测试向量搜索优化
2. **"登记工作站统计面板"** - 测试图片显示
3. **"TERMINAL_LEVEL配置"** - 测试文本回答
4. **"扫描仪集成"** - 测试多图片章节

## 🔧 Web界面功能

### ⚙️ 配置信息显示
- **嵌入模型**: 显示当前使用的向量模型
- **搜索策略**: 智能混合搜索（向量+关键词）
- **检索参数**: TOP_K数量、相似度阈值等

### 🔗 嵌入模型切换
- **text2vec-base-chinese**: 中文优化模型（推荐）
- **text2vec-large-chinese**: 大型中文模型
- **paraphrase-multilingual**: 多语言支持
- **all-MiniLM-L6-v2**: 英文轻量模型

### 🔄 系统维护
- **切换模型**: 一键切换不同的嵌入模型
- **重建向量数据库**: 解决搜索问题

## 🔍 问题排查

### 如果启动失败：
1. 检查虚拟环境是否存在
2. 确认文档文件是否在根目录
3. 查看终端错误信息

### 如果查询无结果：
1. 等待首次初始化完成（可能需要1-2分钟）
2. 尝试使用更简单的关键词
3. 检查网络连接（首次下载模型）

### 如果图片不显示：
1. 检查 extracted_images 目录
2. 确认 section_image_mapping.json 文件
3. 重新构建章节映射

## 🛠️ 高级选项

### 重建系统文件：
```bash
# 重建向量数据库
python utils/rebuild_vector_db.py

# 重建章节映射
python src/section_image_mapper.py
```

### 调试模式：
```bash
# 运行调试脚本
python debug/debug_*.py

# 运行测试
python tests/test_*.py
```

## 📊 系统状态

启动成功的标志：
- ✅ 环境变量设置完成
- ✅ Streamlit服务启动
- ✅ 显示访问URL
- ✅ 配置自动加载

## 💡 使用建议

1. **首次使用**：启动后等待1-2分钟完成初始化
2. **日常使用**：直接运行启动脚本即可
3. **开发调试**：使用Python启动器查看详细信息
4. **生产部署**：建议使用完整版streamlit_app.py

## 🔄 更新说明

- **v1.0**: 基础RAG功能
- **v1.1**: 添加图片显示
- **v1.2**: 结构化章节解析
- **v1.3**: 图片精确位置显示
- **v1.4**: 向量搜索优化 + 环境配置自动化

现在您可以使用 `powershell -ExecutionPolicy Bypass -File quick_start.ps1` 快速启动系统！
