#!/usr/bin/env python3
"""
重新构建章节映射，应用修复
"""
import os
import json
import re

def clean_content_for_display(content: str) -> str:
    """清理内容用于显示，修复格式问题"""
    if not content:
        return ""
    
    # 1. 替换各种空白字符为单个空格
    cleaned = re.sub(r'\s+', ' ', content)
    
    # 2. 修复可能的字符编码问题
    cleaned = cleaned.replace(' :', ':')  # 修复 "r :" -> "r:"
    
    # 3. 修复常见的格式问题
    cleaned = re.sub(r'r\s*:\s*WAIT', 'r:WAIT', cleaned)  # 确保 r:WAIT 格式正确
    cleaned = re.sub(r'r\s*,\s*(\d+)', r'r:WAIT,\1', cleaned)  # 修复 r,0654 -> r:WAIT,0654
    
    # 4. 清理首尾空白
    cleaned = cleaned.strip()
    
    return cleaned

def fix_mapping_file():
    """修复映射文件中的内容"""
    print("=== 修复映射文件 ===")
    
    mapping_file = "section_image_mapping.json"
    backup_file = "section_image_mapping_backup.json"
    
    # 备份原文件
    if os.path.exists(mapping_file):
        import shutil
        shutil.copy2(mapping_file, backup_file)
        print(f"已备份原文件到: {backup_file}")
    
    # 加载原始数据
    with open(mapping_file, 'r', encoding='utf-8') as f:
        section_map = json.load(f)
    
    # 修复所有段落内容
    fixed_count = 0
    total_paragraphs = 0
    
    for section_id, section_info in section_map.items():
        for element in section_info.get('section_elements', []):
            if element.get('type') == 'paragraph':
                total_paragraphs += 1
                original_content = element.get('content', '')
                cleaned_content = clean_content_for_display(original_content)
                
                if original_content != cleaned_content:
                    print(f"修复章节 {section_id} 中的段落:")
                    print(f"  原始: '{original_content[:50]}...'")
                    print(f"  修复: '{cleaned_content[:50]}...'")
                    element['content'] = cleaned_content
                    fixed_count += 1
    
    print(f"\n处理了 {total_paragraphs} 个段落，修复了 {fixed_count} 个段落")
    
    # 保存修复后的文件
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(section_map, f, ensure_ascii=False, indent=2)
    
    print(f"已保存修复后的映射文件: {mapping_file}")
    
    # 验证修复效果
    print("\n=== 验证修复效果 ===")
    for section_id, section_info in section_map.items():
        if "十九、已报告状态下打印报告配置" in section_info.get('section_title', ''):
            for element in section_info.get('section_elements', []):
                if element.get('type') == 'paragraph':
                    content = element.get('content', '')
                    if "打印状态存在" in content:
                        print(f"验证章节 {section_id}:")
                        print(f"内容: '{content}'")
                        
                        if "r:WAIT,0654" in content and "r:WAIT,0655" in content:
                            print("✅ 修复成功!")
                        else:
                            print("❌ 修复失败!")
                        break
            break

def create_simple_test_file():
    """创建一个简单的测试文件来验证Web界面"""
    print("\n=== 创建测试文件 ===")
    
    test_content = """
<!DOCTYPE html>
<html>
<head>
    <title>测试显示效果</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>测试内容显示</h1>
    <h2>原始问题文本（如果有问题）：</h2>
    <p>打印状态存在r
,0654中，打印时间存在r
,0655中</p>
    
    <h2>修复后的文本：</h2>
    <p>打印状态存在r:WAIT,0654中，打印时间存在r:WAIT,0655中</p>
    
    <h2>说明：</h2>
    <p>如果您在Web界面看到的是第一种格式（有换行和冒号丢失），说明需要应用修复。</p>
    <p>如果您看到的是第二种格式（完整的r:WAIT格式），说明修复已生效。</p>
</body>
</html>
"""
    
    with open("test_display.html", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("已创建测试文件: test_display.html")
    print("您可以在浏览器中打开此文件来对比显示效果")

def check_current_web_data():
    """检查当前Web应用可能使用的数据"""
    print("\n=== 检查当前Web数据 ===")
    
    # 检查是否有其他可能的数据文件
    possible_files = [
        "section_image_mapping.json",
        "vector_store",
        "extracted_images"
    ]
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"✅ {file_path} (文件, {size} 字节)")
            else:
                files = os.listdir(file_path)
                print(f"✅ {file_path} (目录, {len(files)} 个文件)")
        else:
            print(f"❌ {file_path} (不存在)")

if __name__ == "__main__":
    check_current_web_data()
    fix_mapping_file()
    create_simple_test_file()
    
    print("\n=== 重要提示 ===")
    print("1. 映射文件已修复")
    print("2. 如果Web应用仍显示问题，请重启Streamlit应用")
    print("3. 重启命令: streamlit run streamlit_lite.py")
    print("4. 或者在浏览器中强制刷新页面 (Ctrl+F5)")
