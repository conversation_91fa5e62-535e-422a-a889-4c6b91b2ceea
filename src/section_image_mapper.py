"""
章节-图片映射器 - 建立章节与图片的直接对应关系
"""
import os
import json
import zipfile
import base64
import re
from typing import Dict, List, Any
from docx import Document
from PIL import Image
import io
from .document_loader import DocumentLoader
from . import config


class SectionImageMapper:
    """章节-图片映射器类"""
    
    def __init__(self):
        self.mapping_file = "section_image_mapping.json"
        self.section_image_map = {}
        self.images_dir = "extracted_images"
        self.ensure_images_dir()

    def ensure_images_dir(self):
        """确保图片目录存在"""
        if not os.path.exists(self.images_dir):
            os.makedirs(self.images_dir)

    def extract_images_from_docx(self, docx_path: str) -> List[Dict[str, Any]]:
        """
        从Word文档中提取图片

        Args:
            docx_path: Word文档路径

        Returns:
            图片信息列表
        """
        images_info = []

        try:
            with zipfile.ZipFile(docx_path, 'r') as docx_zip:
                media_files = [f for f in docx_zip.namelist() if f.startswith('word/media/')]

                for i, media_file in enumerate(media_files):
                    try:
                        image_data = docx_zip.read(media_file)
                        file_ext = os.path.splitext(media_file)[1].lower()

                        if file_ext not in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
                            continue

                        image_filename = f"image_{i+1}{file_ext}"
                        image_path = os.path.join(self.images_dir, image_filename)

                        with open(image_path, 'wb') as img_file:
                            img_file.write(image_data)

                        base64_image = base64.b64encode(image_data).decode('utf-8')

                        try:
                            with Image.open(io.BytesIO(image_data)) as img:
                                width, height = img.size
                        except:
                            width, height = 0, 0

                        images_info.append({
                            'filename': image_filename,
                            'path': image_path,
                            'base64': base64_image,
                            'format': file_ext[1:].upper(),
                            'width': width,
                            'height': height,
                            'size': len(image_data),
                            'media_file': media_file
                        })

                    except Exception as e:
                        print(f"处理图片 {media_file} 时出错: {e}")
                        continue

            print(f"成功提取 {len(images_info)} 张图片")
            return images_info

        except Exception as e:
            print(f"提取图片时出错: {e}")
            return []

    def parse_document_structure(self, docx_path: str) -> List[Dict[str, Any]]:
        """
        解析文档的完整结构，精确定位图片在文字中的位置

        Args:
            docx_path: Word文档路径

        Returns:
            文档结构列表，包含图片的精确位置信息
        """
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"文档文件不存在: {docx_path}")

        try:
            doc = Document(docx_path)
            document_structure = []
            current_section = None
            element_index = 0
            image_counter = 1

            # 提取图片信息
            image_files = self._extract_image_files(docx_path)

            # 遍历文档的所有段落
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()

                # 检查段落中是否包含图片
                images_in_paragraph = self._find_images_in_paragraph(paragraph, image_files, image_counter)

                if text:  # 如果段落有文本内容
                    # 检查是否是标题
                    if self._is_title(text):
                        # 如果有当前章节，先保存
                        if current_section:
                            document_structure.append(current_section)

                        # 开始新章节
                        current_section = {
                            'type': 'section',
                            'title': text,
                            'elements': [],
                            'section_id': len([s for s in document_structure if s.get('type') == 'section']) + 1,
                            'start_index': element_index
                        }

                        # 添加标题元素
                        title_element = {
                            'type': 'title',
                            'content': text,
                            'index': element_index,
                            'has_images_after': len(images_in_paragraph) > 0
                        }

                        if current_section:
                            current_section['elements'].append(title_element)

                    else:  # 普通段落
                        # 如果段落包含图片，需要分割文本
                        if images_in_paragraph:
                            # 将段落分割为文本片段和图片
                            text_parts = self._split_paragraph_with_images(paragraph, images_in_paragraph)

                            for part in text_parts:
                                if part['type'] == 'text' and part['content'].strip():
                                    paragraph_element = {
                                        'type': 'paragraph',
                                        'content': part['content'].strip(),
                                        'index': element_index,
                                        'has_image_before': part.get('has_image_before', False),
                                        'has_image_after': part.get('has_image_after', False)
                                    }

                                    if current_section:
                                        current_section['elements'].append(paragraph_element)
                                    element_index += 1

                                elif part['type'] == 'image':
                                    image_element = {
                                        'type': 'image',
                                        'filename': part['filename'],
                                        'index': element_index,
                                        'position_context': part.get('context', ''),
                                        'before_text': part.get('before_text', ''),
                                        'after_text': part.get('after_text', '')
                                    }

                                    if current_section:
                                        current_section['elements'].append(image_element)
                                    element_index += 1
                                    image_counter += 1
                        else:
                            # 普通段落，没有图片
                            paragraph_element = {
                                'type': 'paragraph',
                                'content': text,
                                'index': element_index,
                                'has_image_before': False,
                                'has_image_after': False
                            }

                            if current_section:
                                current_section['elements'].append(paragraph_element)
                            else:
                                # 如果还没有章节，创建一个默认章节
                                current_section = {
                                    'type': 'section',
                                    'title': '前言',
                                    'elements': [paragraph_element],
                                    'section_id': 1,
                                    'start_index': element_index
                                }

                # 如果段落只有图片没有文字
                elif images_in_paragraph:
                    for img_info in images_in_paragraph:
                        image_element = {
                            'type': 'image',
                            'filename': img_info['filename'],
                            'index': element_index,
                            'position_context': '独立图片段落',
                            'before_text': '',
                            'after_text': ''
                        }

                        if current_section:
                            current_section['elements'].append(image_element)

                        image_counter += 1

                element_index += 1

            # 添加最后一个章节
            if current_section:
                document_structure.append(current_section)

            print(f"文档结构解析完成，共 {len(document_structure)} 个章节")
            return document_structure

        except Exception as e:
            raise Exception(f"解析文档结构时出错: {str(e)}")

    def _split_paragraph_with_images(self, paragraph, images_in_paragraph: List[Dict]) -> List[Dict]:
        """
        将包含图片的段落分割为文本片段和图片

        Args:
            paragraph: Word段落对象
            images_in_paragraph: 段落中的图片列表

        Returns:
            分割后的元素列表
        """
        parts = []
        text = paragraph.text

        if not images_in_paragraph:
            return [{'type': 'text', 'content': text}]

        # 简化处理：将图片插入到段落文本的中间或末尾
        if text.strip():
            # 如果有文本，将图片放在文本后面
            parts.append({
                'type': 'text',
                'content': text,
                'has_image_after': True
            })

            # 添加图片
            for i, img_info in enumerate(images_in_paragraph):
                parts.append({
                    'type': 'image',
                    'filename': img_info['filename'],
                    'context': f"位于段落「{text[:50]}...」之后",
                    'before_text': text[:100] if len(text) > 100 else text,
                    'after_text': ''
                })
        else:
            # 如果没有文本，只有图片
            for img_info in images_in_paragraph:
                parts.append({
                    'type': 'image',
                    'filename': img_info['filename'],
                    'context': '独立图片',
                    'before_text': '',
                    'after_text': ''
                })

        return parts

    def _is_title(self, text: str) -> bool:
        """判断文本是否为标题"""
        title_pattern = re.compile(r'^[一二三四五六七八九十百千万]+、.+$')
        return bool(title_pattern.match(text))

    def _extract_image_files(self, docx_path: str) -> Dict[str, bytes]:
        """提取文档中的图片文件"""
        image_files = {}
        try:
            with zipfile.ZipFile(docx_path, 'r') as docx_zip:
                media_files = [f for f in docx_zip.namelist() if f.startswith('word/media/')]

                for i, media_file in enumerate(media_files):
                    file_ext = os.path.splitext(media_file)[1].lower()
                    if file_ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
                        filename = f"image_{i+1}{file_ext}"
                        image_data = docx_zip.read(media_file)
                        image_files[filename] = image_data
        except Exception as e:
            print(f"提取图片文件时出错: {e}")

        return image_files

    def _find_images_in_paragraph(self, paragraph, image_files: Dict, start_counter: int) -> List[Dict]:
        """查找段落中的图片"""
        images = []
        try:
            drawing_elements = paragraph._element.xpath('.//w:drawing')

            if drawing_elements:
                for i, drawing in enumerate(drawing_elements):
                    image_counter = start_counter + i
                    if image_counter <= len(image_files):
                        possible_extensions = ['.jpg', '.png', '.jpeg', '.gif', '.bmp']
                        filename = None

                        for ext in possible_extensions:
                            test_filename = f"image_{image_counter}{ext}"
                            if test_filename in [f"image_{j+1}{os.path.splitext(f)[1]}" for j, f in enumerate(image_files.keys())]:
                                filename = test_filename
                                break

                        if not filename:
                            filename = f"image_{image_counter}.jpg"

                        images.append({
                            'filename': filename,
                            'position_in_paragraph': i
                        })
        except Exception as e:
            print(f"查找段落图片时出错: {e}")

        return images
        
    def build_section_image_mapping(self) -> Dict[int, Dict[str, Any]]:
        """
        构建章节与图片的映射关系
        
        Returns:
            章节ID到图片信息的映射字典
        """
        print("🔗 构建章节-图片映射关系...")
        
        try:
            # 1. 解析文档结构
            document_structure = self.parse_document_structure(config.DOCUMENT_PATH)

            # 2. 提取图片信息
            images_info = self.extract_images_from_docx(config.DOCUMENT_PATH)
            
            # 3. 建立映射关系
            section_image_map = {}
            
            for section in document_structure:
                section_id = section['section_id']
                section_title = section['title']
                
                # 查找该章节中的所有图片
                section_images = []
                image_positions = []
                
                for i, element in enumerate(section.get('elements', [])):
                    if element['type'] == 'image':
                        # 查找对应的图片详细信息
                        matching_image = None
                        for img_info in images_info:
                            if img_info['filename'] == element['filename']:
                                matching_image = img_info.copy()
                                matching_image['position_in_section'] = i
                                matching_image['element_index'] = element.get('index', i)
                                break
                        
                        if matching_image:
                            section_images.append(matching_image)
                            image_positions.append(i)
                
                # 存储章节信息
                section_image_map[section_id] = {
                    'section_id': section_id,
                    'section_title': section_title,
                    'section_elements': section.get('elements', []),
                    'images': section_images,
                    'image_positions': image_positions,
                    'total_images': len(section_images),
                    'has_images': len(section_images) > 0
                }
            
            self.section_image_map = section_image_map
            
            # 4. 保存映射关系到文件
            self.save_mapping()
            
            # 5. 打印统计信息
            total_sections = len(section_image_map)
            sections_with_images = len([s for s in section_image_map.values() if s['has_images']])
            total_images = sum(s['total_images'] for s in section_image_map.values())
            
            print(f"✅ 映射构建完成:")
            print(f"   总章节数: {total_sections}")
            print(f"   含图片章节: {sections_with_images}")
            print(f"   总图片数: {total_images}")
            
            return section_image_map
            
        except Exception as e:
            print(f"❌ 构建映射关系失败: {e}")
            return {}
    
    def save_mapping(self):
        """保存映射关系到文件"""
        try:
            # 创建可序列化的映射副本
            serializable_map = {}

            for section_id, section_info in self.section_image_map.items():
                # 过滤掉不可序列化的元素
                serializable_elements = []
                for element in section_info.get('section_elements', []):
                    serializable_element = {
                        'type': element['type'],
                        'content': element.get('content', ''),
                        'filename': element.get('filename', ''),
                        'index': element.get('index', 0)
                    }
                    serializable_elements.append(serializable_element)

                serializable_map[section_id] = {
                    'section_id': section_info['section_id'],
                    'section_title': section_info['section_title'],
                    'section_elements': serializable_elements,
                    'images': section_info['images'],
                    'image_positions': section_info['image_positions'],
                    'total_images': section_info['total_images'],
                    'has_images': section_info['has_images']
                }

            with open(self.mapping_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_map, f, ensure_ascii=False, indent=2)
            print(f"📁 映射关系已保存到: {self.mapping_file}")
        except Exception as e:
            print(f"❌ 保存映射关系失败: {e}")
    
    def load_mapping(self) -> bool:
        """从文件加载映射关系"""
        try:
            if os.path.exists(self.mapping_file):
                with open(self.mapping_file, 'r', encoding='utf-8') as f:
                    self.section_image_map = json.load(f)
                
                # 转换键为整数（JSON会将整数键转为字符串）
                self.section_image_map = {
                    int(k): v for k, v in self.section_image_map.items()
                }
                
                print(f"📁 映射关系已从文件加载: {len(self.section_image_map)} 个章节")
                return True
            else:
                print(f"📁 映射文件不存在，需要重新构建")
                return False
        except Exception as e:
            print(f"❌ 加载映射关系失败: {e}")
            return False
    
    def get_section_with_images(self, section_id: int) -> Dict[str, Any]:
        """
        获取指定章节及其所有图片信息
        
        Args:
            section_id: 章节ID
            
        Returns:
            章节完整信息，包含所有图片
        """
        return self.section_image_map.get(section_id, {})
    
    def get_sections_with_images(self, section_ids: List[int]) -> List[Dict[str, Any]]:
        """
        获取多个章节及其图片信息
        
        Args:
            section_ids: 章节ID列表
            
        Returns:
            章节信息列表
        """
        sections = []
        for section_id in section_ids:
            section_info = self.get_section_with_images(section_id)
            if section_info:
                sections.append(section_info)
        return sections
    
    def reconstruct_section_content(self, section_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        重构章节内容，将图片插入到原文中的精确位置

        Args:
            section_info: 章节信息

        Returns:
            重构后的内容元素列表，图片显示在原文的具体位置
        """
        if not section_info:
            return []

        reconstructed_content = []

        # 遍历章节中的所有元素
        for element in section_info.get('section_elements', []):
            if element['type'] == 'title':
                reconstructed_content.append({
                    'type': 'title',
                    'content': element['content'],
                    'has_images_after': element.get('has_images_after', False)
                })

            elif element['type'] == 'paragraph':
                # 添加段落文本
                paragraph_element = {
                    'type': 'paragraph',
                    'content': element['content'],
                    'has_image_before': element.get('has_image_before', False),
                    'has_image_after': element.get('has_image_after', False)
                }
                reconstructed_content.append(paragraph_element)

            elif element['type'] == 'image':
                # 查找对应的图片详细信息
                matching_image = None
                for img in section_info.get('images', []):
                    if img['filename'] == element['filename']:
                        matching_image = img
                        break

                if matching_image:
                    # 添加图片及其位置上下文
                    image_element = {
                        'type': 'image',
                        'filename': element['filename'],
                        'image_info': matching_image,
                        'position': 'original',
                        'position_context': element.get('position_context', ''),
                        'before_text': element.get('before_text', ''),
                        'after_text': element.get('after_text', ''),
                        'context_description': self._generate_position_description(element)
                    }
                    reconstructed_content.append(image_element)

            elif element['type'] == 'table':
                reconstructed_content.append({
                    'type': 'table',
                    'content': element.get('content', [])
                })

        return reconstructed_content

    def _generate_position_description(self, image_element: Dict) -> str:
        """
        生成图片位置的描述文字

        Args:
            image_element: 图片元素信息

        Returns:
            位置描述文字
        """
        before_text = image_element.get('before_text', '')
        after_text = image_element.get('after_text', '')
        context = image_element.get('position_context', '')

        if context:
            return context
        elif before_text:
            # 截取前面文字的关键部分
            before_preview = before_text[:30] + "..." if len(before_text) > 30 else before_text
            if after_text:
                after_preview = after_text[:30] + "..." if len(after_text) > 30 else after_text
                return f"位于「{before_preview}」和「{after_preview}」之间"
            else:
                return f"位于「{before_preview}」之后"
        else:
            return "独立显示的图片"
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """获取映射统计信息"""
        if not self.section_image_map:
            return {}
        
        total_sections = len(self.section_image_map)
        sections_with_images = len([s for s in self.section_image_map.values() if s['has_images']])
        total_images = sum(s['total_images'] for s in self.section_image_map.values())
        
        # 按图片数量分组统计
        image_distribution = {}
        for section_info in self.section_image_map.values():
            img_count = section_info['total_images']
            image_distribution[img_count] = image_distribution.get(img_count, 0) + 1
        
        return {
            'total_sections': total_sections,
            'sections_with_images': sections_with_images,
            'sections_without_images': total_sections - sections_with_images,
            'total_images': total_images,
            'image_distribution': image_distribution,
            'average_images_per_section': total_images / total_sections if total_sections > 0 else 0
        }


if __name__ == "__main__":
    # 测试章节-图片映射器
    mapper = SectionImageMapper()
    
    # 尝试加载现有映射，如果不存在则构建新的
    if not mapper.load_mapping():
        mapping = mapper.build_section_image_mapping()
    
    # 显示统计信息
    stats = mapper.get_mapping_statistics()
    print(f"\n📊 映射统计:")
    print(f"   总章节数: {stats.get('total_sections', 0)}")
    print(f"   含图片章节: {stats.get('sections_with_images', 0)}")
    print(f"   无图片章节: {stats.get('sections_without_images', 0)}")
    print(f"   总图片数: {stats.get('total_images', 0)}")
    print(f"   平均每章节图片数: {stats.get('average_images_per_section', 0):.2f}")
    
    # 显示图片分布
    distribution = stats.get('image_distribution', {})
    print(f"\n📈 图片分布:")
    for img_count, section_count in sorted(distribution.items()):
        print(f"   {img_count}张图片: {section_count}个章节")
    
    # 测试获取含图片的章节
    print(f"\n🖼️ 含图片的章节:")
    for section_id, section_info in mapper.section_image_map.items():
        if section_info['has_images']:
            print(f"   章节{section_id}: {section_info['section_title']} ({section_info['total_images']}张图片)")
            for img in section_info['images']:
                print(f"     📸 {img['filename']}")
