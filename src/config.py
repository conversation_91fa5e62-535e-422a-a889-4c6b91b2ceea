"""
配置文件
"""
import os

# 设置Hugging Face镜像
def setup_hf_mirror():
    """设置Hugging Face镜像地址"""
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
    print("✅ 已设置Hugging Face镜像: https://hf-mirror.com")

# 在导入时自动设置镜像
setup_hf_mirror()
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 文档路径
DOCUMENT_PATH = "RIS问题及解决方法.docx"

# 向量数据库配置
VECTOR_DB_PATH = "vector_store"
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200

# 嵌入模型配置
# 可选的中文嵌入模型（按推荐程度排序）
CHINESE_EMBEDDING_MODELS = [
    "shibing624/text2vec-base-chinese",      # 专门为中文优化的模型
    "GanymedeNil/text2vec-large-chinese",    # 大型中文模型
    "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",  # 多语言模型
    "sentence-transformers/all-MiniLM-L6-v2"  # 原始英文模型（备选）
]

# 当前使用的嵌入模型
EMBEDDING_MODEL = CHINESE_EMBEDDING_MODELS[0]  # 默认使用中文优化模型

def get_available_embedding_models():
    """获取可用的嵌入模型列表"""
    return CHINESE_EMBEDDING_MODELS

def set_embedding_model(model_name: str):
    """设置嵌入模型"""
    global EMBEDDING_MODEL
    if model_name in CHINESE_EMBEDDING_MODELS:
        EMBEDDING_MODEL = model_name
        print(f"✅ 嵌入模型已切换为: {model_name}")
        return True
    else:
        print(f"❌ 不支持的模型: {model_name}")
        return False

# LLM配置 - 可以选择使用OpenAI或本地模型
USE_OPENAI = False  # 设置为True使用OpenAI，False使用本地模型

if USE_OPENAI:
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    LLM_MODEL = "gpt-3.5-turbo"
else:
    # 使用本地模型（这里可以配置其他本地模型）
    LLM_MODEL = "local"

# 检索配置
TOP_K = 10  # 检索最相关的文档数量（增加以获得更多匹配）
SIMILARITY_THRESHOLD = 3.0  # 相似度阈值（FAISS距离分数，越小越相似，大幅提高阈值获得更多结果）
