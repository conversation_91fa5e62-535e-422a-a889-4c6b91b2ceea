"""
RAG系统主类
"""
import os
from typing import List, Dict, Any
from langchain.schema import Document
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain
try:
    from langchain_openai import ChatOpenAI
except ImportError:
    from langchain.llms import OpenAI as ChatOpenAI
from . import config
from .document_loader import DocumentLoader
from .vector_store import VectorStoreManager
from .section_image_mapper import SectionImageMapper


class SimpleLocalLLM:
    """简单的本地LLM模拟器，用于演示"""
    
    def __call__(self, prompt: str) -> str:
        """
        简单的基于规则的回答生成器
        在实际应用中，这里应该调用真正的本地LLM
        """
        # 这是一个简化的实现，实际应用中应该使用真正的LLM
        context_start = prompt.find("上下文信息:")
        question_start = prompt.find("问题:")
        
        if context_start != -1 and question_start != -1:
            context = prompt[context_start:question_start].replace("上下文信息:", "").strip()
            question = prompt[question_start:].replace("问题:", "").strip()
            
            if context:
                return f"根据提供的文档信息，{question}的相关内容如下：\n\n{context[:500]}...\n\n以上信息来源于RIS问题及解决方法文档。"
            else:
                return f"抱歉，我在文档中没有找到关于'{question}'的相关信息。请尝试使用其他关键词进行搜索。"
        
        return "抱歉，我无法理解您的问题。请重新表述您的问题。"


class RAGSystem:
    """RAG系统主类"""
    
    def __init__(self):
        """初始化RAG系统"""
        self.document_loader = DocumentLoader()
        self.vector_manager = VectorStoreManager()
        self.section_mapper = SectionImageMapper()
        self.llm = None
        self.qa_chain = None

        # 初始化LLM
        self._initialize_llm()

        # 创建提示模板
        self._create_prompt_template()

        # 初始化章节-图片映射
        self._initialize_section_mapping()
    
    def _initialize_llm(self):
        """初始化LLM"""
        if config.USE_OPENAI and config.OPENAI_API_KEY:
            # 使用OpenAI
            self.llm = ChatOpenAI(
                model=config.LLM_MODEL,
                temperature=0.1,
                openai_api_key=config.OPENAI_API_KEY
            )
            print("使用OpenAI LLM")
        else:
            # 使用本地模拟LLM
            self.llm = SimpleLocalLLM()
            print("使用本地模拟LLM")
    
    def _create_prompt_template(self):
        """创建提示模板"""
        template = """你是一个专业的RIS系统技术支持助手。请根据以下上下文信息回答用户的问题。

上下文信息:
{context}

问题: {question}

请根据上下文信息提供准确、详细的回答。如果上下文中没有相关信息，请明确说明。

回答:"""
        
        self.prompt_template = PromptTemplate(
            template=template,
            input_variables=["context", "question"]
        )

    def _initialize_section_mapping(self):
        """初始化章节-图片映射"""
        try:
            # 尝试加载现有映射，如果不存在则构建新的
            if not self.section_mapper.load_mapping():
                print("构建章节-图片映射...")
                self.section_mapper.build_section_image_mapping()

            stats = self.section_mapper.get_mapping_statistics()
            print(f"章节-图片映射初始化完成：{stats.get('total_sections', 0)} 个章节，{stats.get('total_images', 0)} 张图片")

        except Exception as e:
            print(f"初始化章节-图片映射失败: {e}")
    
    def initialize_system(self, force_rebuild: bool = False, quick_mode: bool = False) -> bool:
        """
        初始化系统

        Args:
            force_rebuild: 是否强制重建向量存储
            quick_mode: 是否使用快速模式（跳过一些耗时操作）

        Returns:
            是否初始化成功
        """
        try:
            # 检查是否需要重建向量存储
            if force_rebuild:
                print("强制重建向量存储...")
                # 加载文档
                documents = self.document_loader.load_and_split()
                # 创建向量存储
                self.vector_manager.create_vector_store(documents)
                # 保存向量存储
                self.vector_manager.save_vector_store()
            else:
                # 尝试加载现有向量存储
                if not self.vector_manager.load_vector_store():
                    if quick_mode:
                        print("快速模式：跳过向量存储构建，将在首次查询时构建")
                        return True
                    else:
                        print("向量存储不存在，正在构建...")
                        # 加载文档
                        documents = self.document_loader.load_and_split()
                        # 创建向量存储
                        self.vector_manager.create_vector_store(documents)
                        # 保存向量存储
                        self.vector_manager.save_vector_store()
                else:
                    print("向量存储加载成功")

            print("RAG系统初始化完成")
            return True

        except Exception as e:
            print(f"RAG系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def query(self, question: str) -> Dict[str, Any]:
        """
        查询系统（新的简化逻辑：只检索章节，直接获取章节图片）

        Args:
            question: 用户问题

        Returns:
            包含答案和相关信息的字典
        """
        try:
            # 1. 检索相关章节
            docs_with_scores = self.vector_manager.similarity_search_with_score(question)

            # 检查向量搜索结果质量，如果质量差则使用关键词搜索
            if not docs_with_scores or self._should_use_keyword_search(question, docs_with_scores):
                print(f"🔍 向量搜索结果质量差，使用关键词搜索")
                keyword_results = self._fallback_keyword_search(question)
                if keyword_results:
                    docs_with_scores = keyword_results

            # 额外检查：如果是特定查询但没有找到预期章节，强制使用关键词搜索
            elif self._needs_forced_keyword_search(question, docs_with_scores):
                print(f"🔍 强制使用关键词搜索以获得更准确结果")
                keyword_results = self._fallback_keyword_search(question)
                if keyword_results:
                    # 合并结果，关键词搜索结果优先
                    combined_results = keyword_results + docs_with_scores
                    # 去重并限制数量
                    seen_sections = set()
                    unique_results = []
                    for doc, score in combined_results:
                        section_id = doc.metadata.get('section_id')
                        if section_id not in seen_sections:
                            seen_sections.add(section_id)
                            unique_results.append((doc, score))
                    docs_with_scores = unique_results[:config.TOP_K]

            if not docs_with_scores:
                return {
                    "answer": "抱歉，我在文档中没有找到相关信息。请尝试使用其他关键词。",
                    "structured_sections": [],
                    "sources": [],
                    "confidence": 0.0
                }

            # 2. 提取章节ID
            section_ids = []
            context_docs = []
            for doc, score in docs_with_scores:
                section_id = doc.metadata.get('section_id')
                if section_id and section_id not in section_ids:
                    section_ids.append(section_id)
                context_docs.append(doc)

            # 3. 获取章节及其所有图片
            sections_with_images = self.section_mapper.get_sections_with_images(section_ids)

            # 4. 生成文本回答
            context = "\n\n".join([doc.page_content for doc in context_docs])
            prompt = self.prompt_template.format(context=context, question=question)

            if isinstance(self.llm, SimpleLocalLLM):
                answer = self.llm(prompt)
            else:
                answer = self.llm.predict(prompt)

            # 5. 重构章节内容，将图片插入原位
            structured_sections = []
            total_images = 0

            for section_info in sections_with_images:
                reconstructed_content = self.section_mapper.reconstruct_section_content(section_info)

                structured_sections.append({
                    'section_id': section_info['section_id'],
                    'section_title': section_info['section_title'],
                    'content': reconstructed_content,
                    'images': section_info.get('images', []),
                    'total_images': section_info.get('total_images', 0)
                })

                total_images += section_info.get('total_images', 0)

            # 6. 计算置信度
            avg_confidence = sum(score for _, score in docs_with_scores) / len(docs_with_scores)

            return {
                "answer": answer,
                "structured_sections": structured_sections,
                "sources": [
                    {
                        "content": doc.page_content[:200] + "...",
                        "metadata": doc.metadata,
                        "score": score
                    }
                    for doc, score in docs_with_scores
                ],
                "total_sections": len(structured_sections),
                "total_images": total_images,
                "confidence": avg_confidence
            }

        except Exception as e:
            return {
                "answer": f"查询过程中出现错误: {str(e)}",
                "structured_sections": [],
                "sources": [],
                "total_sections": 0,
                "total_images": 0,
                "confidence": 0.0
            }
    
    def add_document(self, file_path: str) -> bool:
        """
        添加新文档到系统
        
        Args:
            file_path: 文档路径
            
        Returns:
            是否添加成功
        """
        try:
            # 加载新文档
            documents = self.document_loader.load_and_split(file_path)
            
            # 添加到向量存储
            self.vector_manager.add_documents(documents)
            
            # 保存更新后的向量存储
            self.vector_manager.save_vector_store()
            
            print(f"成功添加文档: {file_path}")
            return True
            
        except Exception as e:
            print(f"添加文档失败: {e}")
            return False

    def _fallback_keyword_search(self, question: str) -> List[tuple]:
        """
        关键词搜索后备方案

        Args:
            question: 查询问题

        Returns:
            (文档, 分数) 元组列表
        """
        print(f"🔍 向量搜索无结果，使用关键词搜索: {question}")

        try:
            # 从章节映射中搜索
            matching_sections = []

            for section_id, section_info in self.section_mapper.section_image_map.items():
                title = section_info['section_title']

                # 获取章节内容
                content_parts = []
                for element in section_info.get('section_elements', []):
                    if element['type'] == 'paragraph':
                        content_parts.append(element['content'])

                content = '\n'.join(content_parts[:5])  # 取前5段用于匹配

                # 检查标题匹配或内容匹配
                title_match = self._keyword_match(question, title)
                content_match = self._keyword_match(question, content)

                if title_match or content_match:
                    # 创建一个模拟的文档对象
                    from langchain.schema import Document

                    # 用于显示的内容（前3段）
                    display_content = '\n'.join(content_parts[:3])

                    doc = Document(
                        page_content=display_content,
                        metadata={
                            'section_id': section_id,
                            'section_title': title
                        }
                    )

                    # 计算匹配分数（标题匹配权重更高）
                    score = self._calculate_keyword_score(question, title, content, title_match, content_match)
                    matching_sections.append((doc, score))

            # 按分数排序
            matching_sections.sort(key=lambda x: x[1])

            print(f"✅ 关键词搜索找到 {len(matching_sections)} 个匹配章节")
            return matching_sections[:config.TOP_K]

        except Exception as e:
            print(f"❌ 关键词搜索失败: {e}")
            return []

    def _keyword_match(self, query: str, text: str) -> bool:
        """检查关键词匹配（改进版，支持智能分词）"""
        query_lower = query.lower()
        text_lower = text.lower()

        # 直接包含匹配
        if query_lower in text_lower:
            return True

        # 智能分词匹配
        query_words = self._smart_tokenize(query_lower)

        matched_words = 0
        for word in query_words:
            if len(word) >= 2 and word in text_lower:
                matched_words += 1

        # 如果匹配的词数超过总词数的一半，或者至少匹配1个重要词，则认为匹配
        if len(query_words) >= 2:
            return matched_words >= max(1, len(query_words) * 0.5)
        else:
            return matched_words >= 1

    def _smart_tokenize(self, text: str) -> List[str]:
        """智能分词，特别处理常见词组"""
        # 预定义的词组
        predefined_phrases = [
            '登记工作站', '统计面板', '备注项', '扫描仪', '高拍仪',
            '核医学', '检查信息', '当班人员', '配置', 'terminal_level',
            'uv', '集成', '报告', '工作站', '面板'
        ]

        words = []
        remaining_text = text

        # 先匹配预定义词组
        for phrase in predefined_phrases:
            if phrase in remaining_text:
                words.append(phrase)
                remaining_text = remaining_text.replace(phrase, ' ')

        # 再用正则表达式匹配剩余部分
        import re
        additional_words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9_]+', remaining_text)
        words.extend([w for w in additional_words if len(w) >= 2])

        return list(set(words))  # 去重

    def _calculate_keyword_score(self, query: str, title: str, content: str, title_match: bool = False, content_match: bool = False) -> float:
        """计算关键词匹配分数（越小越好，模拟FAISS分数）"""
        score = 2.0  # 基础分数

        query_lower = query.lower()
        title_lower = title.lower()
        content_lower = content.lower()

        # 根据匹配类型给予不同的奖励
        if title_match:
            # 标题匹配权重更高
            if query_lower in title_lower:
                score -= 1.5  # 完全匹配
            else:
                score -= 1.0  # 分词匹配

        if content_match:
            # 内容匹配权重稍低
            if query_lower in content_lower:
                score -= 1.2  # 完全匹配
            else:
                score -= 0.8  # 分词匹配

        # 分词匹配奖励
        import re
        query_words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9_]+', query_lower)

        for word in query_words:
            if len(word) >= 2:
                if word in title_lower:
                    score -= 0.5
                elif word in content_lower:
                    score -= 0.3

        return max(0.1, score)  # 确保分数为正

    def _should_use_keyword_search(self, question: str, docs_with_scores: List[tuple]) -> bool:
        """
        智能判断是否应该使用关键词搜索（基于语义相关性分析）

        Args:
            question: 查询问题
            docs_with_scores: 向量搜索结果

        Returns:
            是否应该使用关键词搜索
        """
        if not docs_with_scores:
            return True

        # 1. 检查向量搜索结果的相似度分数
        # 如果最好的结果分数都很差，说明向量搜索效果不好
        best_score = docs_with_scores[0][1] if docs_with_scores else float('inf')
        if best_score > 2.5:  # FAISS距离分数，越小越相似
            print(f"🔍 向量搜索最佳分数 {best_score:.3f} 过高，切换到关键词搜索")
            return True

        # 2. 智能关键词匹配检查
        query_tokens = self._smart_tokenize(question.lower())

        # 检查前3个结果中是否有语义相关的内容
        semantic_match_count = 0
        for doc, score in docs_with_scores[:3]:
            title = doc.metadata.get('section_title', '').lower()
            content = doc.page_content.lower()

            # 计算查询词在结果中的匹配度
            match_score = 0
            for token in query_tokens:
                if len(token) >= 2:
                    if token in title:
                        match_score += 2  # 标题匹配权重更高
                    elif token in content:
                        match_score += 1

            if match_score >= 1:  # 至少有一个关键词匹配
                semantic_match_count += 1

        # 如果前3个结果中少于2个有语义匹配，使用关键词搜索
        if semantic_match_count < 2:
            print(f"🔍 向量搜索结果语义匹配度低 ({semantic_match_count}/3)，切换到关键词搜索")
            return True

        # 3. 特殊领域词汇检查
        domain_keywords = [
            'uv', '集成', '登记工作站', '统计面板', '备注项', 'terminal_level',
            '工作站', '面板', '高拍仪', '扫描仪', '配置', '报告', '检查'
        ]

        query_has_domain_keyword = any(keyword in question.lower() for keyword in domain_keywords)
        if query_has_domain_keyword:
            # 检查结果中是否有相关的领域词汇
            result_has_domain_match = False
            for doc, score in docs_with_scores[:3]:
                title_content = (doc.metadata.get('section_title', '') + ' ' + doc.page_content).lower()
                if any(keyword in title_content for keyword in domain_keywords if keyword in question.lower()):
                    result_has_domain_match = True
                    break

            if not result_has_domain_match:
                print(f"🔍 查询包含领域关键词但结果中无匹配，切换到关键词搜索")
                return True

        return False

    def _needs_forced_keyword_search(self, question: str, docs_with_scores: List[tuple]) -> bool:
        """
        智能判断是否需要强制使用关键词搜索（基于查询复杂度和结果质量）

        Args:
            question: 查询问题
            docs_with_scores: 向量搜索结果

        Returns:
            是否需要强制关键词搜索
        """
        # 1. 检查查询是否包含多个关键概念
        query_tokens = self._smart_tokenize(question.lower())
        important_tokens = [token for token in query_tokens if len(token) >= 2]

        if len(important_tokens) >= 2:
            # 多概念查询，检查结果是否覆盖了主要概念
            covered_concepts = 0
            for doc, score in docs_with_scores[:3]:
                title_content = (doc.metadata.get('section_title', '') + ' ' + doc.page_content).lower()
                for token in important_tokens:
                    if token in title_content:
                        covered_concepts += 1
                        break

            # 如果前3个结果中少于一半覆盖了查询概念，使用关键词搜索
            if covered_concepts < len(docs_with_scores[:3]) * 0.5:
                print(f"🔍 多概念查询但向量搜索覆盖度低，切换到关键词搜索")
                return True

        # 2. 检查是否为精确匹配查询（如配置名称、功能名称等）
        exact_match_patterns = [
            r'[\u4e00-\u9fff]+配置',  # 中文+配置
            r'[\u4e00-\u9fff]+设置',  # 中文+设置
            r'[\u4e00-\u9fff]+面板',  # 中文+面板
            r'[\u4e00-\u9fff]+工作站', # 中文+工作站
            r'terminal_level',       # 特定配置项
            r'uv',                   # 特定功能
        ]

        import re
        for pattern in exact_match_patterns:
            if re.search(pattern, question.lower()):
                # 检查结果中是否有精确匹配
                has_exact_match = False
                for doc, score in docs_with_scores[:3]:
                    if re.search(pattern, doc.metadata.get('section_title', '').lower()):
                        has_exact_match = True
                        break

                if not has_exact_match:
                    print(f"🔍 精确匹配查询但向量搜索无精确结果，切换到关键词搜索")
                    return True

        return False




if __name__ == "__main__":
    # 测试RAG系统
    rag = RAGSystem()
    
    # 初始化系统
    if rag.initialize_system():
        # 测试查询
        test_questions = [
            "RIS系统有什么常见问题？",
            "如何解决登录问题？",
            "系统性能优化方法"
        ]
        
        for question in test_questions:
            print(f"\n问题: {question}")
            result = rag.query(question)
            print(f"回答: {result['answer']}")
            print(f"置信度: {result['confidence']:.2f}")
    else:
        print("RAG系统初始化失败")
