"""
文档加载器 - 处理Word文档
"""
import os
import re
from typing import List, Dict, Tuple
from docx import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document as LangChainDocument
from . import config


class DocumentLoader:
    """文档加载器类"""
    
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.CHUNK_SIZE,
            chunk_overlap=config.CHUNK_OVERLAP,
            separators=["\n\n", "\n", "。", "！", "？", "；", "，", " ", ""]
        )
        # 标题模式：匹配"一、"、"二十七、"等格式
        self.title_pattern = re.compile(r'^[一二三四五六七八九十百千万]+、.+$')
    
    def load_docx(self, file_path: str) -> str:
        """
        加载Word文档并提取文本
        
        Args:
            file_path: Word文档路径
            
        Returns:
            提取的文本内容
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文档文件不存在: {file_path}")
        
        try:
            doc = Document(file_path)
            text_content = []
            
            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())
            
            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            return "\n\n".join(text_content)
        
        except Exception as e:
            raise Exception(f"加载Word文档时出错: {str(e)}")

    def parse_structured_document(self, file_path: str) -> List[Dict]:
        """
        解析结构化文档，识别标题和内容

        Args:
            file_path: Word文档路径

        Returns:
            结构化的章节列表，每个章节包含标题和内容
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文档文件不存在: {file_path}")

        try:
            doc = Document(file_path)
            sections = []
            current_section = None

            # 遍历所有段落
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if not text:
                    continue

                # 检查是否是标题
                if self.title_pattern.match(text):
                    # 保存上一个章节
                    if current_section:
                        sections.append(current_section)

                    # 开始新章节
                    current_section = {
                        'title': text,
                        'content': [],
                        'section_id': len(sections) + 1
                    }
                else:
                    # 添加到当前章节的内容
                    if current_section:
                        current_section['content'].append(text)
                    else:
                        # 如果还没有标题，创建一个默认章节
                        if not sections:
                            current_section = {
                                'title': '前言',
                                'content': [text],
                                'section_id': 1
                            }

            # 添加最后一个章节
            if current_section:
                sections.append(current_section)

            # 处理表格内容
            for table in doc.tables:
                table_content = []
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        table_content.append(" | ".join(row_text))

                # 将表格内容添加到最后一个章节
                if sections and table_content:
                    sections[-1]['content'].extend(table_content)

            print(f"解析完成，共识别 {len(sections)} 个章节")
            return sections

        except Exception as e:
            raise Exception(f"解析结构化文档时出错: {str(e)}")
    
    def split_text(self, text: str) -> List[LangChainDocument]:
        """
        将文本分割成块
        
        Args:
            text: 要分割的文本
            
        Returns:
            分割后的文档块列表
        """
        # 使用文本分割器分割文本
        chunks = self.text_splitter.split_text(text)
        
        # 创建LangChain文档对象
        documents = []
        for i, chunk in enumerate(chunks):
            doc = LangChainDocument(
                page_content=chunk,
                metadata={
                    "source": config.DOCUMENT_PATH,
                    "chunk_id": i,
                    "chunk_size": len(chunk)
                }
            )
            documents.append(doc)
        
        return documents

    def create_structured_documents(self, sections: List[Dict]) -> List[LangChainDocument]:
        """
        基于结构化章节创建文档块

        Args:
            sections: 结构化章节列表

        Returns:
            LangChain文档对象列表
        """
        documents = []

        for section in sections:
            # 合并标题和内容
            full_content = f"{section['title']}\n\n" + "\n\n".join(section['content'])

            # 如果内容太长，进行分割
            if len(full_content) > config.CHUNK_SIZE:
                # 使用文本分割器分割
                chunks = self.text_splitter.split_text(full_content)

                for i, chunk in enumerate(chunks):
                    doc = LangChainDocument(
                        page_content=chunk,
                        metadata={
                            "source": config.DOCUMENT_PATH,
                            "section_id": section['section_id'],
                            "section_title": section['title'],
                            "chunk_id": i,
                            "chunk_size": len(chunk),
                            "is_structured": True
                        }
                    )
                    documents.append(doc)
            else:
                # 整个章节作为一个文档块
                doc = LangChainDocument(
                    page_content=full_content,
                    metadata={
                        "source": config.DOCUMENT_PATH,
                        "section_id": section['section_id'],
                        "section_title": section['title'],
                        "chunk_id": 0,
                        "chunk_size": len(full_content),
                        "is_structured": True
                    }
                )
                documents.append(doc)

        return documents

    def load_and_split(self, file_path: str = None, use_structured: bool = True) -> List[LangChainDocument]:
        """
        加载文档并分割成块

        Args:
            file_path: 文档路径，默认使用配置中的路径
            use_structured: 是否使用结构化解析

        Returns:
            分割后的文档块列表
        """
        if file_path is None:
            file_path = config.DOCUMENT_PATH

        if use_structured:
            # 使用结构化解析
            sections = self.parse_structured_document(file_path)
            documents = self.create_structured_documents(sections)
            print(f"结构化文档加载完成，共 {len(sections)} 个章节，{len(documents)} 个文档块")
        else:
            # 使用传统方法
            text = self.load_docx(file_path)
            documents = self.split_text(text)
            print(f"文档加载完成，共分割为 {len(documents)} 个文档块")

        return documents

    def get_sections_info(self, file_path: str = None) -> List[Dict]:
        """
        获取文档章节信息

        Args:
            file_path: 文档路径，默认使用配置中的路径

        Returns:
            章节信息列表
        """
        if file_path is None:
            file_path = config.DOCUMENT_PATH

        sections = self.parse_structured_document(file_path)
        return sections


if __name__ == "__main__":
    # 测试文档加载器
    loader = DocumentLoader()
    try:
        # 测试结构化解析
        print("=== 测试结构化解析 ===")
        sections = loader.get_sections_info()
        print(f"识别到 {len(sections)} 个章节:")
        for i, section in enumerate(sections[:5]):  # 只显示前5个
            print(f"  {i+1}. {section['title']}")
            print(f"     内容长度: {len(' '.join(section['content']))} 字符")

        print("\n=== 测试文档分割 ===")
        docs = loader.load_and_split()
        print(f"成功加载文档，共 {len(docs)} 个块")

        if docs:
            print(f"\n第一个块的信息:")
            print(f"  标题: {docs[0].metadata.get('section_title', '无')}")
            print(f"  章节ID: {docs[0].metadata.get('section_id', '无')}")
            print(f"  内容预览: {docs[0].page_content[:200]}...")

    except Exception as e:
        print(f"测试失败: {e}")
