"""
向量存储管理器
"""
import os
from typing import List, Optional
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.schema import Document
from . import config


class VectorStoreManager:
    """向量存储管理器类"""
    
    def __init__(self):
        """初始化向量存储管理器"""
        # 初始化嵌入模型
        self.embeddings = HuggingFaceEmbeddings(
            model_name=config.EMBEDDING_MODEL,
            model_kwargs={'device': 'cpu'},  # 使用CPU
            encode_kwargs={'normalize_embeddings': True}
        )
        self.vector_store = None
    
    def create_vector_store(self, documents: List[Document]) -> None:
        """
        创建向量存储
        
        Args:
            documents: 文档列表
        """
        if not documents:
            raise ValueError("文档列表不能为空")
        
        print("正在创建向量存储...")
        print(f"正在处理 {len(documents)} 个文档块...")
        
        # 创建FAISS向量存储
        self.vector_store = FAISS.from_documents(
            documents=documents,
            embedding=self.embeddings
        )
        
        print("向量存储创建完成")
    
    def save_vector_store(self, path: str = None) -> None:
        """
        保存向量存储到磁盘
        
        Args:
            path: 保存路径，默认使用配置中的路径
        """
        if self.vector_store is None:
            raise ValueError("向量存储未初始化，请先创建向量存储")
        
        if path is None:
            path = config.VECTOR_DB_PATH
        
        # 确保目录存在
        os.makedirs(path, exist_ok=True)
        
        # 保存向量存储
        self.vector_store.save_local(path)
        print(f"向量存储已保存到: {path}")
    
    def load_vector_store(self, path: str = None) -> bool:
        """
        从磁盘加载向量存储
        
        Args:
            path: 加载路径，默认使用配置中的路径
            
        Returns:
            是否加载成功
        """
        if path is None:
            path = config.VECTOR_DB_PATH
        
        if not os.path.exists(path):
            print(f"向量存储路径不存在: {path}")
            return False
        
        try:
            # 加载向量存储
            self.vector_store = FAISS.load_local(
                path, 
                self.embeddings,
                allow_dangerous_deserialization=True
            )
            print(f"向量存储已从 {path} 加载")
            return True
        except Exception as e:
            print(f"加载向量存储失败: {e}")
            return False
    
    def similarity_search(self, query: str, k: int = None) -> List[Document]:
        """
        相似度搜索
        
        Args:
            query: 查询文本
            k: 返回的文档数量，默认使用配置中的值
            
        Returns:
            相似的文档列表
        """
        if self.vector_store is None:
            raise ValueError("向量存储未初始化")
        
        if k is None:
            k = config.TOP_K
        
        # 执行相似度搜索
        docs = self.vector_store.similarity_search(query, k=k)
        return docs

    def _expand_query(self, query: str) -> List[str]:
        """
        扩展查询，添加同义词和变体

        Args:
            query: 原始查询

        Returns:
            扩展后的查询列表
        """
        expanded_queries = [query]

        # 定义同义词映射
        synonyms = {
            '集成UV': ['UV集成', 'UV配置', '集成配置', 'UV设置', 'UV'],
            '登记工作站': ['工作站', '登记站', '工作站配置'],
            '统计面板': ['面板', '统计界面', '统计显示'],
            '备注项': ['备注', '备注配置', '备注设置'],
            '扫描仪': ['扫描仪集成', '扫描设备', '扫描配置'],
            '高拍仪': ['高拍仪集成', '高拍设备', '高拍配置'],
            'TERMINAL_LEVEL': ['TERMINAL', 'LEVEL', '终端级别', '终端配置']
        }

        # 添加同义词
        for key, values in synonyms.items():
            if key in query:
                expanded_queries.extend(values)
            elif query in values:
                expanded_queries.append(key)
                expanded_queries.extend([v for v in values if v != query])

        return list(set(expanded_queries))  # 去重

    def similarity_search_with_score(self, query: str, k: int = None) -> List[tuple]:
        """
        带分数的相似度搜索（增强版，支持查询扩展）

        Args:
            query: 查询文本
            k: 返回的文档数量，默认使用配置中的值

        Returns:
            (文档, 分数) 元组列表
        """
        if self.vector_store is None:
            raise ValueError("向量存储未初始化")

        if k is None:
            k = config.TOP_K

        try:
            # 扩展查询
            expanded_queries = self._expand_query(query)
            all_results = []

            # 对每个扩展查询进行搜索
            for expanded_query in expanded_queries:
                try:
                    docs_with_scores = self.vector_store.similarity_search_with_score(expanded_query, k=k*2)
                    all_results.extend(docs_with_scores)
                except:
                    continue

            # 如果扩展查询没有结果，使用原始查询
            if not all_results:
                docs_with_scores = self.vector_store.similarity_search_with_score(query, k=k*2)
                all_results.extend(docs_with_scores)

            # 去重（基于文档的section_id）
            seen_sections = set()
            unique_results = []
            for doc, score in all_results:
                section_id = doc.metadata.get('section_id')
                if section_id and section_id not in seen_sections:
                    seen_sections.add(section_id)
                    unique_results.append((doc, score))

            # 按分数排序
            unique_results.sort(key=lambda x: x[1])

            # 过滤高于阈值的结果（FAISS分数越小越相似）
            filtered_docs = [
                (doc, score) for doc, score in unique_results
                if score <= config.SIMILARITY_THRESHOLD
            ]

            return filtered_docs[:k]

        except Exception as e:
            print(f"相似度搜索失败: {e}")
            # 降级到简单搜索
            try:
                docs_with_scores = self.vector_store.similarity_search_with_score(query, k=k)
                filtered_docs = [
                    (doc, score) for doc, score in docs_with_scores
                    if score <= config.SIMILARITY_THRESHOLD
                ]
                return filtered_docs
            except:
                return []
    
    def add_documents(self, documents: List[Document]) -> None:
        """
        向现有向量存储添加文档
        
        Args:
            documents: 要添加的文档列表
        """
        if self.vector_store is None:
            raise ValueError("向量存储未初始化")
        
        self.vector_store.add_documents(documents)
        print(f"已添加 {len(documents)} 个文档到向量存储")


if __name__ == "__main__":
    # 测试向量存储管理器
    from document_loader import DocumentLoader
    
    # 加载文档
    loader = DocumentLoader()
    try:
        docs = loader.load_and_split()
        
        # 创建向量存储
        vector_manager = VectorStoreManager()
        vector_manager.create_vector_store(docs)
        vector_manager.save_vector_store()
        
        # 测试搜索
        results = vector_manager.similarity_search("RIS系统问题", k=2)
        print(f"搜索结果数量: {len(results)}")
        if results:
            print(f"第一个结果: {results[0].page_content[:200]}...")
            
    except Exception as e:
        print(f"测试失败: {e}")
