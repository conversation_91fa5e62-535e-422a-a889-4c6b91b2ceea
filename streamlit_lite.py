"""
轻量级Streamlit应用 - 快速启动版本
"""
import streamlit as st
import os
import sys

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.rag_system import RAGSystem
import src.config as config
import re

# 页面配置
st.set_page_config(
    page_title="RIS问题解答系统 - 轻量版",
    page_icon="🤖",
    layout="wide"
)

def check_prerequisites():
    """检查前置条件"""
    missing = []
    
    if not os.path.exists(config.DOCUMENT_PATH):
        missing.append("文档文件")
    if not os.path.exists(config.VECTOR_DB_PATH):
        missing.append("向量数据库")
    if not os.path.exists('section_image_mapping.json'):
        missing.append("章节映射文件")
    
    return missing

def clean_content_for_web_display(content: str) -> str:
    """清理内容用于Web显示，修复格式问题"""
    if not content:
        return ""

    # 1. 替换各种空白字符为单个空格
    cleaned = re.sub(r'\s+', ' ', content)

    # 2. 修复可能的字符编码问题
    cleaned = cleaned.replace(' :', ':')  # 修复 "r :" -> "r:"

    # 3. 修复常见的格式问题
    cleaned = re.sub(r'r\s*:\s*WAIT', 'r:WAIT', cleaned)  # 确保 r:WAIT 格式正确
    cleaned = re.sub(r'r\s*,\s*(\d+)', r'r:WAIT,\1', cleaned)  # 修复 r,0654 -> r:WAIT,0654

    # 4. 清理首尾空白
    cleaned = cleaned.strip()

    return cleaned

@st.cache_resource
def load_rag_system_lite():
    """轻量级RAG系统加载"""
    try:
        rag = RAGSystem()
        # 使用快速模式，延迟加载向量数据库
        if rag.initialize_system(quick_mode=True):
            return rag
        return None
    except Exception as e:
        st.error(f"系统初始化失败: {e}")
        return None

def switch_embedding_model(model_name: str):
    """切换嵌入模型"""
    try:
        with st.spinner(f"🔄 正在切换到模型: {model_name.split('/')[-1]}..."):
            # 切换模型
            success = config.set_embedding_model(model_name)

            if success:
                # 清除缓存
                st.cache_resource.clear()

                # 重新初始化RAG系统
                rag = RAGSystem()
                init_success = rag.initialize_system(force_rebuild=True)

                if init_success:
                    st.success(f"✅ 模型切换成功: {model_name.split('/')[-1]}")
                    st.info("💡 请刷新页面以使用新模型")

                    # 更新session state
                    st.session_state.rag_system = rag
                else:
                    st.error("❌ 新模型初始化失败")
            else:
                st.error(f"❌ 模型切换失败: 不支持的模型")

    except Exception as e:
        st.error(f"❌ 模型切换过程中出现错误: {str(e)}")
        st.info("💡 可能是网络问题或模型下载失败，请稍后重试")

def rebuild_vector_database():
    """重建向量数据库"""
    try:
        with st.spinner("🔄 正在重建向量数据库..."):
            # 清除缓存
            st.cache_resource.clear()

            # 重新初始化RAG系统
            rag = RAGSystem()
            success = rag.initialize_system(force_rebuild=True)

            if success:
                st.success("✅ 向量数据库重建成功！")
                st.info("💡 请刷新页面以使用新的向量数据库")

                # 更新session state
                st.session_state.rag_system = rag
            else:
                st.error("❌ 向量数据库重建失败")

    except Exception as e:
        st.error(f"❌ 重建过程中出现错误: {str(e)}")

def get_system_info():
    """获取系统信息"""
    try:
        return {
            'embedding_model': config.EMBEDDING_MODEL,
            'llm_mode': '本地模拟',
            'top_k': config.TOP_K,
            'similarity_threshold': config.SIMILARITY_THRESHOLD
        }
    except:
        return None

def main():
    """主函数"""
    st.title("🤖 RIS问题解答系统")
    st.markdown("基于RAG技术的智能问答系统 - 轻量版")
    
    # 检查前置条件
    missing = check_prerequisites()
    if missing:
        st.error(f"❌ 缺少必要文件: {', '.join(missing)}")
        st.markdown("### 🔧 解决方案:")
        st.code("""
# 1. 确保文档文件存在
# 2. 重建向量数据库
python utils/rebuild_vector_db.py

# 3. 重建章节映射
python src/section_image_mapper.py
        """)
        return
    
    # 显示系统状态和配置信息
    with st.sidebar:
        # 系统状态区域
        st.markdown("### 📊 系统状态")
        with st.container():
            st.markdown("""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                ✅ 文档文件<br>
                ✅ 向量数据库<br>
                ✅ 章节映射
            </div>
            """, unsafe_allow_html=True)

        # 配置信息区域
        st.markdown("### ⚙️ 配置信息")
        system_info = get_system_info()

        # 添加强制刷新按钮
        st.markdown("### 🔄 系统控制")
        if st.button("🔄 强制刷新数据", help="清除缓存并重新加载所有数据"):
            st.cache_resource.clear()
            st.success("✅ 缓存已清除，页面将重新加载")
            st.rerun()

        if system_info:
            # 嵌入模型信息
            model_name = system_info['embedding_model']
            model_display = model_name.split('/')[-1] if '/' in model_name else model_name
            chinese_tag = "🇨🇳 中文优化" if 'chinese' in model_name.lower() else ""

            st.markdown(f"""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                🔗 嵌入模型: {model_display}<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<em>{chinese_tag}</em>
            </div>
            """, unsafe_allow_html=True)

            st.markdown(f"""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                🤖 LLM模式: {system_info['llm_mode']}
            </div>
            """, unsafe_allow_html=True)

            st.markdown(f"""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                📊 检索数量: {system_info['top_k']}
            </div>
            """, unsafe_allow_html=True)

            st.markdown(f"""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                📏 相似度阈值: {system_info['similarity_threshold']}
            </div>
            """, unsafe_allow_html=True)

            st.markdown(f"""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                🧠 搜索策略: 智能混合搜索<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<em>向量搜索 + 关键词搜索</em>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.warning("⚠️ 无法获取系统信息")

        # 系统维护区域
        st.markdown("### 🔧 系统维护")
        with st.container():
            st.markdown("""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
            """, unsafe_allow_html=True)

            # 向量模型切换
            st.markdown("**🔗 嵌入模型切换**")
            available_models = config.get_available_embedding_models()
            current_model = config.EMBEDDING_MODEL

            # 简化模型名称显示
            model_display_names = []
            model_descriptions = {
                "shibing624/text2vec-base-chinese": "text2vec-base-chinese (推荐中文)",
                "GanymedeNil/text2vec-large-chinese": "text2vec-large-chinese (大型中文)",
                "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2": "paraphrase-multilingual (多语言)",
                "sentence-transformers/all-MiniLM-L6-v2": "all-MiniLM-L6-v2 (英文轻量)"
            }

            for model in available_models:
                display_name = model_descriptions.get(model, model.split('/')[-1])
                model_display_names.append(display_name)

            # 找到当前模型的索引
            try:
                current_index = available_models.index(current_model)
            except ValueError:
                current_index = 0

            selected_model_display = st.selectbox(
                "选择模型:",
                model_display_names,
                index=current_index,
                help="不同模型对中文的理解能力不同，推荐使用中文优化模型",
                label_visibility="collapsed"
            )

            # 获取实际模型名
            selected_model_index = model_display_names.index(selected_model_display)
            selected_model = available_models[selected_model_index]

            # 操作按钮
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 切换模型", help="切换到选中的嵌入模型", use_container_width=True):
                    switch_embedding_model(selected_model)

            with col2:
                if st.button("🔄 重建数据库", help="重新构建向量数据库，解决搜索问题", use_container_width=True):
                    rebuild_vector_database()

            st.markdown("</div>", unsafe_allow_html=True)

        # 使用提示
        st.markdown("### 💡 使用提示")
        with st.container():
            st.markdown("""
            <div style="background-color: #e8f4fd; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                ℹ️ 首次查询可能需要几秒钟加载时间
            </div>
            """, unsafe_allow_html=True)
    
    # 加载RAG系统
    if 'rag_system' not in st.session_state:
        with st.spinner("🔧 正在初始化系统..."):
            st.session_state.rag_system = load_rag_system_lite()
    
    rag = st.session_state.rag_system
    if not rag:
        st.error("❌ 系统初始化失败")
        return
    
    # 查询界面
    st.markdown("### 💬 问题查询")

    # 输入框区域
    # 使用session_state来保持输入状态
    if 'query_input' not in st.session_state:
        st.session_state.query_input = ""

    question = st.text_input(
        "请输入您的问题:",
        value=st.session_state.query_input,
        placeholder="例如：TERMINAL_LEVEL怎么配置？",
        key="question_input",
        help="输入问题后按Enter键或点击搜索按钮"
    )

    # 更新session_state
    st.session_state.query_input = question

    # 按钮区域 - 放在输入框下面，使用更小的按钮
    col1, col2, col3, col4 = st.columns([1, 1, 2, 2])

    with col1:
        # 搜索按钮
        search_clicked = st.button("🔍 搜索", type="primary")

    with col2:
        # 清空按钮
        if st.button("🗑️ 清空"):
            st.session_state.query_input = ""
            st.rerun()

    # col3 和 col4 留空，用于平衡布局
    
    # 处理查询 - 支持按钮触发或直接输入
    should_search = question and (search_clicked or question != st.session_state.get('last_query', ''))

    if should_search:
        # 记录当前查询，避免重复搜索
        st.session_state.last_query = question

        # 添加到搜索历史
        if 'search_history' not in st.session_state:
            st.session_state.search_history = []

        if question not in st.session_state.search_history:
            st.session_state.search_history.append(question)
            # 限制历史记录数量
            if len(st.session_state.search_history) > 10:
                st.session_state.search_history = st.session_state.search_history[-10:]

        with st.spinner("🔍 正在搜索相关信息..."):
            try:
                result = rag.query(question)
                
                if result:
                    # 显示搜索状态
                    st.success(f"✅ 搜索完成 - 查询: \"{question}\"")

                    # 显示统计信息
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("相关章节", result.get('total_sections', 0))
                    with col2:
                        st.metric("相关图片", result.get('total_images', 0))
                    with col3:
                        st.metric("置信度", f"{result.get('confidence', 0):.2f}")
                    with col4:
                        # 添加操作按钮
                        if st.button("🔄 重新搜索", key="research"):
                            st.session_state.last_query = ""  # 清除缓存，强制重新搜索
                            st.rerun()

                    # 显示回答
                    st.markdown("### 🤖 系统回答")
                    st.write(result['answer'])
                    
                    # 显示章节内容（简化版）
                    if result.get('structured_sections'):
                        st.markdown("### 📖 相关章节")
                        
                        for section in result['structured_sections']:
                            with st.expander(f"📋 {section['section_title']}"):
                                for element in section['content']:
                                    if element['type'] == 'paragraph':
                                        # 确保内容正确显示，避免格式问题
                                        content = element['content']
                                        # 额外的内容清理，确保Web显示正确
                                        cleaned_content = clean_content_for_web_display(content)
                                        # 使用st.markdown而不是st.write，可以更好地控制格式
                                        st.markdown(f"**段落内容：** {cleaned_content}")
                                    elif element['type'] == 'image':
                                        try:
                                            import base64
                                            img_info = element['image_info']
                                            image_data = base64.b64decode(img_info['base64'])
                                            st.image(image_data, caption=element['filename'])
                                        except:
                                            st.info(f"📷 {element['filename']}")
                else:
                    st.warning("❌ 未找到相关信息")
                    
            except Exception as e:
                st.error(f"❌ 查询过程中出现错误: {str(e)}")
                st.info("💡 提示: 如果是首次使用，请稍等片刻让系统完成初始化")

if __name__ == "__main__":
    main()
