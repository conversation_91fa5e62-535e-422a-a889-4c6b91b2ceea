# RIS RAG系统项目结构

## 📁 目录结构

```
ris-rag-system/
├── 📄 核心文件
│   ├── main.py                      # 命令行主程序
│   ├── streamlit_lite.py           # Web界面（唯一界面）
│   ├── requirements.txt            # Python依赖包
│   └── README.md                   # 项目说明
│
├── 🚀 启动脚本
│   ├── quick_start.ps1             # PowerShell启动脚本（推荐）
│   ├── quick_start.bat             # 批处理启动脚本
│   ├── start_system.py             # Python启动器
│   └── run_tests.py                # 测试运行器
│
├── 📚 源代码 (src/)
│   ├── __init__.py                 # 包初始化
│   ├── config.py                   # 系统配置
│   ├── document_loader.py          # 文档加载器
│   ├── vector_store.py             # 向量存储管理
│   ├── section_image_mapper.py     # 章节-图片映射
│   └── rag_system.py              # RAG系统主类
│
├── 🧪 测试代码 (tests/)
│   ├── __init__.py
│   ├── test_system.py              # 系统基础测试
│   ├── test_vector_store.py        # 向量存储测试
│   └── test_simplified_rag.py      # 简化RAG测试
│
├── 🔧 调试工具 (debug/)
│   ├── __init__.py
│   ├── debug_context.py            # 上下文调试
│   ├── debug_image_search.py       # 图片搜索调试
│   ├── debug_images.py             # 图片处理调试
│   └── debug_rag_init.py           # RAG初始化调试
│
├── 🛠️ 工具脚本 (utils/)
│   ├── __init__.py
│   └── rebuild_vector_db.py        # 重建向量数据库
│
├── 📊 数据文件
│   ├── RIS问题及解决方法.docx      # 知识库文档
│   ├── section_image_mapping.json  # 章节-图片映射文件
│   ├── extracted_images/           # 提取的图片目录
│   │   ├── image_1.jpg
│   │   ├── image_2.png
│   │   └── ...
│   └── vector_store/               # 向量数据库目录
│       ├── index.faiss
│       └── index.pkl
│
└── 📖 文档
    ├── PROJECT_STRUCTURE.md        # 项目结构说明（本文件）
    ├── STARTUP_GUIDE.md            # 启动指南
    └── .gitignore                  # Git忽略文件
```

## 🚀 快速开始

### 1. 启动Web界面（推荐）
```powershell
# 使用PowerShell脚本
powershell -ExecutionPolicy Bypass -File quick_start.ps1

# 访问地址: http://localhost:8512
```

### 2. 启动命令行版本
```bash
python main.py
```

### 3. 运行测试
```bash
python run_tests.py
```

## 📦 核心模块说明

### src/ - 核心源代码
- **config.py**: 系统配置参数（相似度阈值、模型配置等）
- **document_loader.py**: 文档加载和结构化解析
- **vector_store.py**: 向量存储管理（FAISS + 关键词搜索）
- **section_image_mapper.py**: 章节与图片的精确映射
- **rag_system.py**: RAG系统主类，统一查询接口

### 主要程序
- **streamlit_lite.py**: Web界面（唯一界面）
- **main.py**: 命令行界面

## 🔧 系统特性

### 核心功能
- ✅ **智能搜索**: 向量搜索 + 关键词搜索双重保障
- ✅ **图片精确定位**: 图片显示在原文档的具体位置
- ✅ **章节结构化**: 完整保持文档的章节结构
- ✅ **多界面支持**: Web界面 + 命令行界面

### 搜索优化
- ✅ **智能分词**: 正确处理中文词组
- ✅ **同义词扩展**: 自动扩展相关查询词
- ✅ **质量检测**: 自动检测搜索结果质量
- ✅ **后备机制**: 向量搜索失败时自动切换到关键词搜索

### 图片处理
- ✅ **原位显示**: 图片显示在原文档的具体文字位置
- ✅ **上下文提取**: 提供图片前后的文字上下文
- ✅ **多格式支持**: 支持JPG、PNG等多种图片格式

## 📊 数据统计

- **总章节数**: 53
- **含图片章节**: 7
- **总图片数**: 10
- **图片覆盖率**: 13.2%

## 🔄 开发流程

### 添加新功能
1. 在 `src/` 目录添加核心代码
2. 在 `tests/` 目录添加测试
3. 在 `debug/` 目录添加调试脚本
4. 更新相关文档

### 问题调试
1. 使用 `debug/` 目录中的调试脚本
2. 运行相关测试验证修复
3. 使用Web界面的"重建向量数据库"功能

## 📝 代码规范

- 所有核心功能放在 `src/` 目录
- 测试代码放在 `tests/` 目录
- 调试代码放在 `debug/` 目录
- 工具脚本放在 `utils/` 目录
- 使用相对导入引用同包模块
- 添加适当的文档字符串和注释

## 🌟 版本历史

- **v1.0**: 基础RAG功能
- **v1.1**: 添加图片显示
- **v1.2**: 结构化章节解析
- **v1.3**: 图片精确位置显示
- **v1.4**: 向量搜索优化 + 环境配置自动化
- **v1.5**: Web界面配置显示 + 重建功能
