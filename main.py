"""
RAG系统主程序
"""
import os
import sys

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

from src.rag_system import RAGSystem
import src.config as config


def print_welcome():
    """打印欢迎信息"""
    print("=" * 60)
    print("🤖 RIS问题解答系统")
    print("=" * 60)
    print("基于RAG技术的智能问答系统")
    print(f"文档来源: {config.DOCUMENT_PATH}")
    print("输入 'quit' 或 'exit' 退出系统")
    print("输入 'help' 查看帮助信息")
    print("=" * 60)


def print_help():
    """打印帮助信息"""
    print("\n📖 帮助信息:")
    print("- 直接输入问题进行查询")
    print("- 支持中文问题，如：'RIS系统登录问题如何解决？'")
    print("- 输入 'rebuild' 重建向量数据库")
    print("- 输入 'status' 查看系统状态")
    print("- 输入 'quit' 或 'exit' 退出系统")


def print_status(rag_system):
    """打印系统状态"""
    print("\n📊 系统状态:")
    print(f"- 文档路径: {config.DOCUMENT_PATH}")
    print(f"- 文档存在: {'✅' if os.path.exists(config.DOCUMENT_PATH) else '❌'}")
    print(f"- 向量数据库: {'✅' if os.path.exists(config.VECTOR_DB_PATH) else '❌'}")
    print(f"- 嵌入模型: {config.EMBEDDING_MODEL}")
    print(f"- LLM模式: {'OpenAI' if config.USE_OPENAI else '本地模拟'}")
    print(f"- 检索数量: {config.TOP_K}")
    print(f"- 相似度阈值: {config.SIMILARITY_THRESHOLD}")


def format_result(result):
    """格式化查询结果"""
    print("\n" + "=" * 50)
    print("🤖 回答:")
    print("-" * 50)
    print(result['answer'])
    
    # 显示相关图片信息
    if result.get('images'):
        print(f"\n🖼️ 相关图片:")
        print("-" * 50)
        for i, img_info in enumerate(result['images'], 1):
            print(f"{i}. {img_info['filename']} ({img_info['format']}, {img_info['width']}x{img_info['height']})")
            if 'relevance_score' in img_info:
                print(f"   相关性: {img_info['relevance_score']:.2f}")

            # 显示章节信息
            if 'section_info' in img_info:
                section_info = img_info['section_info']
                print(f"   📖 关联章节: {section_info['section_title']}")
                print(f"   📋 章节ID: {section_info['section_id']}")

            print(f"   📁 路径: {img_info['path']}")
            if i < len(result['images']):
                print()

    if result['sources']:
        print(f"\n📚 参考来源 (置信度: {result['confidence']:.2f}):")
        print("-" * 50)
        for i, source in enumerate(result['sources'], 1):
            print(f"{i}. {source['content']}")
            print(f"   相似度: {source['score']:.3f}")
            if i < len(result['sources']):
                print()

    print("=" * 50)


def main():
    """主函数"""
    print_welcome()
    
    # 检查文档是否存在
    if not os.path.exists(config.DOCUMENT_PATH):
        print(f"❌ 错误: 找不到文档文件 '{config.DOCUMENT_PATH}'")
        print("请确保文档文件在当前目录中")
        return
    
    # 初始化RAG系统
    print("🔄 正在初始化RAG系统...")
    rag_system = RAGSystem()
    
    if not rag_system.initialize_system():
        print("❌ RAG系统初始化失败")
        return
    
    print("✅ RAG系统初始化成功！")
    print("\n💡 您可以开始提问了！")
    
    # 主循环
    while True:
        try:
            # 获取用户输入
            user_input = input("\n🔍 请输入您的问题: ").strip()
            
            # 检查退出命令
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 感谢使用RIS问题解答系统！")
                break
            
            # 检查帮助命令
            if user_input.lower() in ['help', '帮助']:
                print_help()
                continue
            
            # 检查状态命令
            if user_input.lower() in ['status', '状态']:
                print_status(rag_system)
                continue
            
            # 检查重建命令
            if user_input.lower() in ['rebuild', '重建']:
                print("🔄 正在重建向量数据库...")
                if rag_system.initialize_system(force_rebuild=True):
                    print("✅ 向量数据库重建成功！")
                else:
                    print("❌ 向量数据库重建失败")
                continue
            
            # 检查空输入
            if not user_input:
                print("⚠️  请输入有效的问题")
                continue
            
            # 处理查询
            print("🔍 正在搜索相关信息...")
            result = rag_system.query(user_input)
            
            # 显示结果
            format_result(result)
            
        except KeyboardInterrupt:
            print("\n\n👋 感谢使用RIS问题解答系统！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            print("请重试或输入 'help' 查看帮助")


if __name__ == "__main__":
    main()
