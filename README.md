# 🤖 RIS问题解答系统

基于RAG技术的智能问答系统，专为RIS系统问题的快速查询和解答而设计。

## ✨ 核心特性

- 🔍 **智能搜索**: 向量搜索 + 关键词搜索双重保障
- 🖼️ **图片精确定位**: 图片显示在原文档的具体文字位置
- 📚 **结构化展示**: 完整保持文档的章节结构
- 🌐 **多界面支持**: Web界面 + 命令行界面
- ⚡ **快速启动**: 一键启动脚本，自动环境配置

## 🚀 快速开始

### 方法1: 使用启动脚本（推荐）
```powershell
# Windows PowerShell
powershell -ExecutionPolicy Bypass -File quick_start.ps1

# 访问地址: http://localhost:8512
```

### 方法2: 手动启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动Web界面
streamlit run streamlit_lite.py

# 3. 或启动命令行界面
python main.py
```

## 📊 系统数据

- **总章节数**: 53
- **含图片章节**: 7  
- **总图片数**: 10
- **支持格式**: DOCX文档 + JPG/PNG图片

## 🎯 推荐测试问题

1. **"登记工作站统计面板"** → 查看图片显示功能
2. **"集成UV"** → 测试向量搜索优化
3. **"TERMINAL_LEVEL配置"** → 测试文本回答
4. **"扫描仪集成"** → 测试多图片章节

## 🔧 Web界面功能

- ⚙️ **配置信息显示**: 嵌入模型、检索参数等
- 📈 **数据统计**: 章节数、图片数、覆盖率
- 🔄 **一键重建**: 重建向量数据库功能
- 🖼️ **图片原位显示**: 图片显示在原文档位置

## 📁 项目结构

```
ris-rag-system/
├── src/                    # 核心源代码
├── tests/                  # 测试代码
├── debug/                  # 调试工具
├── utils/                  # 工具脚本
├── streamlit_lite.py       # Web界面
├── main.py                 # 命令行界面
└── quick_start.ps1         # 快速启动脚本
```

详细结构请查看 [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)

## 🛠️ 技术架构

- **文档处理**: 结构化解析DOCX文档
- **向量存储**: FAISS + sentence-transformers
- **搜索策略**: 向量搜索 + 关键词后备
- **图片处理**: 精确位置映射 + Base64编码
- **Web框架**: Streamlit轻量级界面

## 📖 使用指南

详细的启动和使用指南请查看 [STARTUP_GUIDE.md](STARTUP_GUIDE.md)

## 🔄 版本信息

当前版本: **v1.5**
- ✅ 智能搜索优化
- ✅ 图片精确位置显示  
- ✅ Web界面配置显示
- ✅ 一键重建功能
