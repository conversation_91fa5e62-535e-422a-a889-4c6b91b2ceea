# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# System Files
.DS_Store
Thumbs.db

# Project Specific
vector_store/
extracted_images/
section_image_mapping.json
*.log

# Streamlit
.streamlit/

# Temporary files
temp/
tmp/
*.tmp
*.bak
*.backup
*_backup.*

# Temporary debug and test files
debug_*.py
test_*.py
*_test.py
*_debug.py

# Documentation drafts
*_DEMO.md
*_ISSUES.md
