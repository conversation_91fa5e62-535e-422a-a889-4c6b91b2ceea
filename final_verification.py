#!/usr/bin/env python3
"""
最终验证所有修复
"""
import json
import re

def clean_content_for_web_display(content: str) -> str:
    """清理内容用于Web显示，修复格式问题"""
    if not content:
        return ""
    
    # 1. 替换各种空白字符为单个空格
    cleaned = re.sub(r'\s+', ' ', content)
    
    # 2. 修复可能的字符编码问题
    cleaned = cleaned.replace(' :', ':')  # 修复 "r :" -> "r:"
    
    # 3. 修复常见的格式问题
    cleaned = re.sub(r'r\s*:\s*WAIT', 'r:WAIT', cleaned)  # 确保 r:WAIT 格式正确
    cleaned = re.sub(r'r\s*,\s*(\d+)', r'r:WAIT,\1', cleaned)  # 修复 r,0654 -> r:WAIT,0654
    
    # 4. 清理首尾空白
    cleaned = cleaned.strip()
    
    return cleaned

def verify_all_fixes():
    """验证所有修复"""
    print("=== 最终验证所有修复 ===")
    
    # 1. 验证映射文件
    print("1. 验证映射文件...")
    mapping_file = "section_image_mapping.json"
    with open(mapping_file, 'r', encoding='utf-8') as f:
        section_map = json.load(f)
    
    # 找到第19章
    target_found = False
    for section_id, section_info in section_map.items():
        if "十九、已报告状态下打印报告配置" in section_info.get('section_title', ''):
            target_found = True
            print(f"✅ 找到目标章节: {section_info['section_title']}")
            
            for element in section_info.get('section_elements', []):
                if element.get('type') == 'paragraph':
                    content = element.get('content', '')
                    if "打印状态存在" in content:
                        print(f"📄 映射文件中的内容:")
                        print(f"   '{content[:80]}...'")
                        
                        if "r:WAIT,0654" in content and "r:WAIT,0655" in content:
                            print("✅ 映射文件中的内容正确")
                        else:
                            print("❌ 映射文件中的内容有问题")
                        break
            break
    
    if not target_found:
        print("❌ 未找到目标章节")
        return
    
    # 2. 验证Web清理函数
    print("\n2. 验证Web清理函数...")
    test_cases = [
        "打印状态存在r:WAIT,0654中，打印时间存在r:WAIT,0655中",  # 正常
        "打印状态存在r\n,0654中，打印时间存在r\n,0655中",  # 有换行
        "打印状态存在r ,0654中，打印时间存在r ,0655中",  # 冒号丢失
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        cleaned = clean_content_for_web_display(test_case)
        print(f"测试 {i}: {repr(test_case[:30])}...")
        print(f"结果: {repr(cleaned[:30])}...")
        
        if "r:WAIT,0654" in cleaned and "r:WAIT,0655" in cleaned:
            print("✅ 清理函数工作正常")
        else:
            print("❌ 清理函数有问题")
    
    # 3. 验证Streamlit文件修改
    print("\n3. 验证Streamlit文件修改...")
    try:
        with open("streamlit_lite.py", 'r', encoding='utf-8') as f:
            streamlit_content = f.read()
        
        if "clean_content_for_web_display" in streamlit_content:
            print("✅ Streamlit文件包含清理函数")
        else:
            print("❌ Streamlit文件缺少清理函数")
        
        if "强制刷新数据" in streamlit_content:
            print("✅ Streamlit文件包含强制刷新按钮")
        else:
            print("❌ Streamlit文件缺少强制刷新按钮")
    
    except Exception as e:
        print(f"❌ 检查Streamlit文件时出错: {e}")
    
    # 4. 生成用户指导
    print("\n=== 用户操作指导 ===")
    print("现在请按以下步骤操作:")
    print("1. 如果Streamlit应用正在运行，请停止它 (Ctrl+C)")
    print("2. 重新启动Streamlit应用:")
    print("   streamlit run streamlit_lite.py")
    print("3. 在浏览器中打开应用")
    print("4. 在侧边栏点击 '🔄 强制刷新数据' 按钮")
    print("5. 搜索 '打印状态' 或 '十九' 来测试修复效果")
    print("6. 检查显示的内容是否为:")
    print("   '打印状态存在r:WAIT,0654中，打印时间存在r:WAIT,0655中'")
    print("   而不是:")
    print("   '打印状态存在r\\n,0654中，打印时间存在r\\n,0655中'")

def create_startup_script():
    """创建启动脚本"""
    print("\n=== 创建启动脚本 ===")
    
    startup_script = """#!/bin/bash
# 启动修复后的Streamlit应用

echo "=== RIS问题解答系统启动脚本 ==="
echo "正在启动Streamlit应用..."

# 设置环境变量
export HF_ENDPOINT=https://hf-mirror.com
export TOKENIZERS_PARALLELISM=false

# 启动Streamlit
streamlit run streamlit_lite.py --server.port 8501 --server.address 0.0.0.0

echo "应用已启动，请在浏览器中访问: http://localhost:8501"
"""
    
    with open("start_app.sh", "w", encoding="utf-8") as f:
        f.write(startup_script)
    
    # Windows批处理文件
    windows_script = """@echo off
REM 启动修复后的Streamlit应用

echo === RIS问题解答系统启动脚本 ===
echo 正在启动Streamlit应用...

REM 设置环境变量
set HF_ENDPOINT=https://hf-mirror.com
set TOKENIZERS_PARALLELISM=false

REM 启动Streamlit
streamlit run streamlit_lite.py --server.port 8501 --server.address 0.0.0.0

echo 应用已启动，请在浏览器中访问: http://localhost:8501
pause
"""
    
    with open("start_app.bat", "w", encoding="utf-8") as f:
        f.write(windows_script)
    
    print("✅ 已创建启动脚本:")
    print("   - Linux/Mac: start_app.sh")
    print("   - Windows: start_app.bat")

if __name__ == "__main__":
    verify_all_fixes()
    create_startup_script()
    
    print("\n" + "="*60)
    print("🎉 所有修复已完成!")
    print("请按照上述指导重新启动应用并测试修复效果。")
    print("="*60)
