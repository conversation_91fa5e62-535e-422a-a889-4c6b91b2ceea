# RIS RAG System Quick Start Script (PowerShell)

Write-Host "RIS RAG System Quick Start" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

Write-Host "Setting environment variables..." -ForegroundColor Yellow
$env:HF_ENDPOINT = "https://hf-mirror.com"
$env:TOKENIZERS_PARALLELISM = "false"

Write-Host "Environment variables set successfully" -ForegroundColor Green

Write-Host "Starting lightweight web interface..." -ForegroundColor Yellow
Write-Host "Access URL: http://localhost:8512" -ForegroundColor Cyan

# Activate virtual environment and start Streamlit
& .venv\Scripts\activate.ps1
streamlit run streamlit_lite.py --server.port 8512
