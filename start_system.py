"""
系统启动脚本 - 自动设置环境并启动
"""
import os
import sys
import subprocess

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 设置Hugging Face镜像
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
    print("✅ 已设置 HF_ENDPOINT = https://hf-mirror.com")
    
    # 设置其他可能需要的环境变量
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'  # 避免tokenizer警告
    print("✅ 已设置 TOKENIZERS_PARALLELISM = false")

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查系统前置条件...")
    
    missing = []
    
    # 检查文档文件
    if not os.path.exists('RIS问题及解决方法.docx'):
        missing.append("RIS问题及解决方法.docx")
    
    # 检查虚拟环境
    if not os.path.exists('.venv'):
        missing.append("虚拟环境 (.venv)")
    
    # 检查src目录
    if not os.path.exists('src'):
        missing.append("src目录")
    
    if missing:
        print("❌ 缺少必要文件:")
        for item in missing:
            print(f"   - {item}")
        return False
    
    print("✅ 前置条件检查通过")
    return True

def start_streamlit_lite():
    """启动轻量版Streamlit"""
    print("🚀 启动轻量版Streamlit...")
    
    try:
        # 构建启动命令
        if os.name == 'nt':  # Windows
            cmd = [
                'powershell', '-Command',
                '.venv\\Scripts\\activate.ps1; streamlit run streamlit_lite.py --server.port 8512'
            ]
        else:  # Linux/Mac
            cmd = [
                'bash', '-c',
                'source .venv/bin/activate && streamlit run streamlit_lite.py --server.port 8512'
            ]
        
        # 启动进程
        process = subprocess.Popen(cmd, cwd=os.getcwd())
        
        print("✅ Streamlit启动成功!")
        print("🌐 访问地址: http://localhost:8512")
        print("💡 按 Ctrl+C 停止服务")
        
        # 等待进程
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            process.terminate()
            print("✅ 服务已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_command_line():
    """启动命令行版本"""
    print("💻 启动命令行版本...")
    
    try:
        # 导入并运行主程序
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from main import main
        main()
    except Exception as e:
        print(f"❌ 命令行启动失败: {e}")

def show_menu():
    """显示启动菜单"""
    print("🤖 RIS问题解答系统启动器")
    print("=" * 50)
    print("请选择启动方式:")
    print("1. 🌐 Web界面 (轻量版)")
    print("2. 💻 命令行界面")
    print("3. 🔍 系统检查")
    print("4. 🚪 退出")
    print("=" * 50)
    
    while True:
        try:
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == '1':
                start_streamlit_lite()
                break
            elif choice == '2':
                start_command_line()
                break
            elif choice == '3':
                run_system_check()
            elif choice == '4':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 输入错误: {e}")

def run_system_check():
    """运行系统检查"""
    print("\n🔍 运行系统检查...")
    
    try:
        # 导入配置以触发环境设置
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        import src.config as config
        
        print("✅ 配置加载成功")
        
        # 检查章节映射
        if os.path.exists('section_image_mapping.json'):
            print("✅ 章节映射文件存在")
        else:
            print("⚠️ 章节映射文件不存在，首次运行时会自动创建")
        
        # 检查向量数据库
        if os.path.exists('vector_store'):
            print("✅ 向量数据库目录存在")
        else:
            print("⚠️ 向量数据库不存在，首次运行时会自动创建")
        
        # 检查图片目录
        if os.path.exists('extracted_images'):
            print("✅ 图片目录存在")
        else:
            print("⚠️ 图片目录不存在，首次运行时会自动创建")
        
        print("✅ 系统检查完成")
        
    except Exception as e:
        print(f"❌ 系统检查失败: {e}")
    
    print()  # 空行，返回菜单

def main():
    """主函数"""
    # 设置环境
    setup_environment()
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n🔧 请先解决上述问题后再启动系统")
        return
    
    # 显示菜单
    show_menu()

if __name__ == "__main__":
    main()
